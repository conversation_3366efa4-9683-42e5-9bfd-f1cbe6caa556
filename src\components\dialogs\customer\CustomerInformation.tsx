
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

interface Customer {
  id?: string;
  name: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address: string;
  state?: string;
  city?: string;
  pincode?: string;
}

interface CustomerInformationProps {
  customer: Customer;
}

export function CustomerInformation({ customer }: CustomerInformationProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Customer Name</Label>
          <Input value={customer.name} readOnly className="bg-gray-50" />
        </div>
        <div>
          <Label>Email Address</Label>
          <Input value={customer.email} readOnly className="bg-gray-50" />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Phone Number</Label>
          <Input value={customer.phone} readOnly className="bg-gray-50" />
        </div>
        <div>
          <Label>Alternate Phone</Label>
          <Input value={customer.alternatePhone || ""} readOnly className="bg-gray-50" />
        </div>
      </div>

      <div>
        <Label>Address</Label>
        <Textarea value={customer.address} readOnly className="bg-gray-50" rows={2} />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label>State</Label>
          <Input value={customer.state || ""} readOnly className="bg-gray-50" />
        </div>
        <div>
          <Label>City</Label>
          <Input value={customer.city || ""} readOnly className="bg-gray-50" />
        </div>
        <div>
          <Label>Pincode</Label>
          <Input value={customer.pincode || ""} readOnly className="bg-gray-50" />
        </div>
      </div>
    </div>
  );
}
