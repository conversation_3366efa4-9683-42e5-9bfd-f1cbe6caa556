
import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Plus, X } from "lucide-react";
import type { InventoryItem, UsedPart } from "../../../types";

interface PartsSectionProps {
  inventory: InventoryItem[];
  usedParts: UsedPart[];
  selectedPart: string;
  setSelectedPart: (part: string) => void;
  partQuantity: number;
  setPartQuantity: (quantity: number) => void;
  onAddPart: () => void;
  onRemovePart: (index: number) => void;
}

export function PartsSection({
  inventory,
  usedParts,
  selectedPart,
  setSelectedPart,
  partQuantity,
  setPartQuantity,
  onAddPart,
  onRemovePart
}: PartsSectionProps) {
  const [showOtherPart, setShowOtherPart] = useState(false);
  const [otherPartName, setOtherPartName] = useState("");
  const [manufacturer, setManufacturer] = useState("");
  const [model, setModel] = useState("");
  const [serialNo, setSerialNo] = useState("");

  const handlePartSelection = (value: string) => {
    setSelectedPart(value);
    setShowOtherPart(value === "others");
    if (value !== "others") {
      setOtherPartName("");
      setManufacturer("");
      setModel("");
      setSerialNo("");
    }
  };

  const handleAddPart = () => {
    if (selectedPart === "others" && otherPartName.trim()) {
      // This will be handled by the parent component
      onAddPart();
      setOtherPartName("");
      setManufacturer("");
      setModel("");
      setSerialNo("");
      setShowOtherPart(false);
      setSelectedPart("");
    } else if (selectedPart && selectedPart !== "others") {
      onAddPart();
    }
  };

  return (
    <div className="space-y-3">
      <Label>Parts Used</Label>
      
      <div className="space-y-2">
        <div className="flex gap-2">
          <Select value={selectedPart} onValueChange={handlePartSelection}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select part" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              {inventory.map(item => (
                <SelectItem key={item.id} value={item.id}>
                  {item.name} (Stock: {item.currentStock})
                </SelectItem>
              ))}
              <SelectItem value="others">Others (Custom Part)</SelectItem>
            </SelectContent>
          </Select>
          <Input
            type="number"
            min="1"
            value={partQuantity}
            onChange={(e) => setPartQuantity(parseInt(e.target.value) || 1)}
            className="w-20"
            placeholder="Qty"
          />
          <Button type="button" onClick={handleAddPart} size="sm">
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>
      
      {showOtherPart && (
        <div className="space-y-3 p-3 border rounded-lg bg-gray-50">
          <div>
            <Label htmlFor="otherPart">Custom Part Name</Label>
            <Input
              id="otherPart"
              value={otherPartName}
              onChange={(e) => setOtherPartName(e.target.value)}
              placeholder="Enter part name"
              className="mt-1"
            />
          </div>
          <div className="grid grid-cols-3 gap-2">
            <div>
              <Label htmlFor="manufacturer">Manufacturer</Label>
              <Input
                id="manufacturer"
                value={manufacturer}
                onChange={(e) => setManufacturer(e.target.value)}
                placeholder="Manufacturer"
              />
            </div>
            <div>
              <Label htmlFor="model">Model</Label>
              <Input
                id="model"
                value={model}
                onChange={(e) => setModel(e.target.value)}
                placeholder="Model"
              />
            </div>
            <div>
              <Label htmlFor="serialNo">Serial No</Label>
              <Input
                id="serialNo"
                value={serialNo}
                onChange={(e) => setSerialNo(e.target.value)}
                placeholder="Serial No"
              />
            </div>
          </div>
        </div>
      )}

      {usedParts && usedParts.length > 0 && (
        <div className="space-y-2">
          {usedParts.map((part, index) => (
            <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
              <div className="flex-1">
                <span className="font-medium">{part.name} x{part.quantity}</span>
                {(part.manufacturer || part.model || part.serialNo) && (
                  <div className="text-xs text-gray-600 mt-1">
                    {part.manufacturer && `Manufacturer: ${part.manufacturer} `}
                    {part.model && `Model: ${part.model} `}
                    {part.serialNo && `S/N: ${part.serialNo}`}
                  </div>
                )}
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onRemovePart(index)}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
