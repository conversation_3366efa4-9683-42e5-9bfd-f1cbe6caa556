
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from "recharts";
import { Calendar, Download, TrendingUp, DollarSign, Users, Package, Wrench, Receipt } from "lucide-react";
import { useAppData } from "@/contexts/AppDataContext";

export default function Reports() {
  const { services, customers, inventory, expenses, invoices } = useAppData();
  const [activeTab, setActiveTab] = useState("Inventory");

  // Calculate inventory data
  const totalInventoryValue = inventory.reduce((sum, item) => 
    sum + (item.currentStock * parseFloat(item.unitPrice.replace('₹', '').replace(',', ''))), 0
  );
  const lowStockItems = inventory.filter(item => item.currentStock <= item.minimumStock);
  
  // Calculate service data
  const servicesByStatus = [
    { name: 'Pending', value: services.filter(s => s.status === 'Pending').length, color: '#fbbf24' },
    { name: 'In Progress', value: services.filter(s => s.status === 'In Progress').length, color: '#3b82f6' },
    { name: 'Completed', value: services.filter(s => s.status === 'Completed').length, color: '#10b981' },
  ];

  // Calculate monthly service performance
  const servicePerformance = [
    { month: 'Jan', completed: 25, pending: 8, revenue: 85000 },
    { month: 'Feb', completed: 30, pending: 5, revenue: 92000 },
    { month: 'Mar', completed: 28, pending: 12, revenue: 88000 },
    { month: 'Apr', completed: 35, pending: 7, revenue: 105000 },
    { month: 'May', completed: 32, pending: 9, revenue: 98000 },
    { month: 'Jun', completed: 40, pending: 6, revenue: 120000 },
  ];

  // Calculate customer data
  const totalRevenue = invoices.reduce((sum, invoice) => 
    sum + parseFloat(invoice.amount.replace('₹', '').replace(',', '')), 0
  );
  
  const topCustomers = customers
    .map(customer => ({
      name: customer.name,
      services: services.filter(s => s.customer === customer.name).length,
      revenue: invoices.filter(i => i.customer === customer.name)
        .reduce((sum, invoice) => sum + parseFloat(invoice.amount.replace('₹', '').replace(',', '')), 0)
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);

  // Calculate profit and loss
  const totalExpenses = expenses.reduce((sum, expense) => 
    sum + parseFloat(expense.amount.replace('₹', '').replace(',', '')), 0
  );
  const profit = totalRevenue - totalExpenses;

  const profitLossData = [
    { month: 'Jan', revenue: 120000, expenses: 45000, profit: 75000 },
    { month: 'Feb', revenue: 135000, expenses: 48000, profit: 87000 },
    { month: 'Mar', revenue: 128000, expenses: 52000, profit: 76000 },
    { month: 'Apr', revenue: 145000, expenses: 50000, profit: 95000 },
    { month: 'May', revenue: 158000, expenses: 55000, profit: 103000 },
    { month: 'Jun', revenue: 172000, expenses: 58000, profit: 114000 },
  ];

  // Calculate inventory by category
  const inventoryByCategory = Array.from(new Set(inventory.map(item => item.category)))
    .map(category => ({
      name: category,
      value: inventory.filter(item => item.category === category).length,
      color: `hsl(${Math.random() * 360}, 70%, 50%)`
    }));

  // Calculate expenses by category
  const expensesByCategory = Array.from(new Set(expenses.map(expense => expense.category)))
    .map(category => ({
      name: category,
      value: expenses.filter(expense => expense.category === category)
        .reduce((sum, expense) => sum + parseFloat(expense.amount.replace('₹', '').replace(',', '')), 0),
      color: `hsl(${Math.random() * 360}, 70%, 50%)`
    }));

  const renderInventoryReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Inventory Value by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={inventoryByCategory}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {inventoryByCategory.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Stock Levels Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span>Total Items</span>
                <span className="font-bold">{inventory.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Total Value</span>
                <span className="font-bold">₹{totalInventoryValue.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Low Stock Items</span>
                <span className="font-bold text-red-600">{lowStockItems.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span>Categories</span>
                <span className="font-bold">{inventoryByCategory.length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderServiceReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Service Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={servicesByStatus}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {servicesByStatus.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Service Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={servicePerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="completed" fill="#10b981" />
                <Bar dataKey="pending" fill="#fbbf24" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderCustomerReport = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Top Customers by Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={topCustomers} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" />
              <YAxis dataKey="name" type="category" width={100} />
              <Tooltip />
              <Bar dataKey="revenue" fill="#f97316" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderProfitLossReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">₹{totalRevenue.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">₹{totalExpenses.toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Profit</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ₹{profit.toLocaleString()}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Profit & Loss Trend</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={profitLossData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="revenue" stroke="#10b981" strokeWidth={3} />
              <Line type="monotone" dataKey="expenses" stroke="#ef4444" strokeWidth={3} />
              <Line type="monotone" dataKey="profit" stroke="#f97316" strokeWidth={3} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderExpensesReport = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Expenses by Category</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={expensesByCategory}
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={120}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {expensesByCategory.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Expense Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={profitLossData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="expenses" fill="#ef4444" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case "Inventory":
        return renderInventoryReport();
      case "Service":
        return renderServiceReport();
      case "Customer":
        return renderCustomerReport();
      case "Profit & Loss":
        return renderProfitLossReport();
      case "Expenses":
        return renderExpensesReport();
      default:
        return renderInventoryReport();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            This Month
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      <p className="text-gray-600">Comprehensive business analytics and reporting</p>

      {/* Report Tabs */}
      <div className="flex space-x-2 border-b border-gray-200">
        {["Inventory", "Service", "Customer", "Profit & Loss", "Expenses"].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? "default" : "ghost"}
            onClick={() => setActiveTab(tab)}
            className={activeTab === tab ? "bg-orange-500 hover:bg-orange-600" : ""}
          >
            {tab === "Inventory" && <Package className="w-4 h-4 mr-2" />}
            {tab === "Service" && <Wrench className="w-4 h-4 mr-2" />}
            {tab === "Customer" && <Users className="w-4 h-4 mr-2" />}
            {tab === "Profit & Loss" && <TrendingUp className="w-4 h-4 mr-2" />}
            {tab === "Expenses" && <Receipt className="w-4 h-4 mr-2" />}
            {tab}
          </Button>
        ))}
      </div>

      {renderContent()}
    </div>
  );
}
