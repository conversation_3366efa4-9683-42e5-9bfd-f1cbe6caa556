
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';
import { Constants } from '@/integrations/supabase/types';

type InvoiceRow = Database['public']['Tables']['invoices']['Row'];
type InvoiceInsert = Database['public']['Tables']['invoices']['Insert'];
type InvoiceUpdate = Database['public']['Tables']['invoices']['Update'];
type UsedPartRow = Database['public']['Tables']['used_parts']['Row'];
type UsedPartInsert = Database['public']['Tables']['used_parts']['Insert'];
type CommentRow = Database['public']['Tables']['comments']['Row'];
type CommentInsert = Database['public']['Tables']['comments']['Insert'];

export interface UsedPart {
  id: string;
  itemId: string;
  name: string;
  quantity: number;
  price: string;
  manufacturer?: string;
  model?: string;
  serialNo?: string;
}

export interface Comment {
  id: string;
  text: string;
  author: string;
  timestamp: string;
  type: typeof Constants.public.Enums.comment_type[number];
}

export interface Invoice {
  id: string;
  customer: string;
  date: string;
  amount: string;
  status: typeof Constants.public.Enums.invoice_status[number];
  device: string;
  deviceType: string;
  customDeviceName: string | null;
  issue: string;
  gst: string;
  phone: string;
  alternatePhone: string | null;
  address: string;
  state: string | null;
  city: string | null;
  pincode: string | null;
  estimatedAmount: string;
  billableWarranty: string | null;
  showRemarks: boolean;
  remarks: string | null;
  expectedDelivery: string;
  inspectionFee: string;
  usedParts: UsedPart[];
  comments: Comment[];
  created_at: string;
}

// Convert from DB format to app format
function convertFromDb(
  dbInvoice: InvoiceRow, 
  usedParts: UsedPartRow[] = [], 
  comments: CommentRow[] = []
): Invoice {
  return {
    id: dbInvoice.id,
    customer: dbInvoice.customer_name,
    date: dbInvoice.date,
    amount: dbInvoice.amount.toString(),
    status: dbInvoice.status,
    device: dbInvoice.device || '',
    deviceType: dbInvoice.device_type || '',
    customDeviceName: dbInvoice.custom_device_name,
    issue: dbInvoice.issue || '',
    gst: dbInvoice.gst?.toString() || '',
    phone: dbInvoice.phone || '',
    alternatePhone: dbInvoice.alternate_phone,
    address: dbInvoice.address || '',
    state: dbInvoice.state,
    city: dbInvoice.city,
    pincode: dbInvoice.pincode,
    estimatedAmount: dbInvoice.estimated_amount?.toString() || '',
    billableWarranty: dbInvoice.billable_warranty,
    showRemarks: dbInvoice.show_remarks || false,
    remarks: dbInvoice.remarks,
    expectedDelivery: dbInvoice.expected_delivery || '',
    inspectionFee: dbInvoice.inspection_fee?.toString() || '500',
    usedParts: usedParts.map(part => ({
      id: part.id,
      itemId: part.item_id || 'custom-' + Date.now(),
      name: part.name,
      quantity: part.quantity,
      price: part.price.toString(),
      manufacturer: part.manufacturer || undefined,
      model: part.model || undefined,
      serialNo: part.serial_no || undefined
    })),
    comments: comments.map(comment => ({
      id: comment.id,
      text: comment.text,
      author: comment.author,
      timestamp: comment.created_at,
      type: comment.type
    })),
    created_at: dbInvoice.created_at
  };
}

// Convert to DB insert format
function convertToDbInsert(invoice: Omit<Invoice, 'id' | 'usedParts' | 'comments' | 'created_at'>): InvoiceInsert {
  return {
    customer_name: invoice.customer,
    date: invoice.date,
    amount: parseFloat(invoice.amount || '0'),
    status: invoice.status,
    device: invoice.device,
    device_type: invoice.deviceType,
    custom_device_name: invoice.customDeviceName,
    issue: invoice.issue,
    gst: invoice.gst ? parseFloat(invoice.gst) : null,
    phone: invoice.phone,
    alternate_phone: invoice.alternatePhone,
    address: invoice.address,
    state: invoice.state,
    city: invoice.city,
    pincode: invoice.pincode,
    estimated_amount: invoice.estimatedAmount ? parseFloat(invoice.estimatedAmount) : null,
    billable_warranty: invoice.billableWarranty,
    show_remarks: invoice.showRemarks,
    remarks: invoice.remarks,
    expected_delivery: invoice.expectedDelivery,
    inspection_fee: invoice.inspectionFee ? parseFloat(invoice.inspectionFee) : 500
  };
}

// Convert to DB update format
function convertToDbUpdate(invoice: Partial<Invoice>): InvoiceUpdate {
  const update: InvoiceUpdate = {};
  
  if (invoice.customer !== undefined) update.customer_name = invoice.customer;
  if (invoice.date !== undefined) update.date = invoice.date;
  if (invoice.amount !== undefined) update.amount = parseFloat(invoice.amount || '0');
  if (invoice.status !== undefined) update.status = invoice.status;
  if (invoice.device !== undefined) update.device = invoice.device;
  if (invoice.deviceType !== undefined) update.device_type = invoice.deviceType;
  if (invoice.customDeviceName !== undefined) update.custom_device_name = invoice.customDeviceName;
  if (invoice.issue !== undefined) update.issue = invoice.issue;
  if (invoice.gst !== undefined) update.gst = invoice.gst ? parseFloat(invoice.gst) : null;
  if (invoice.phone !== undefined) update.phone = invoice.phone;
  if (invoice.alternatePhone !== undefined) update.alternate_phone = invoice.alternatePhone;
  if (invoice.address !== undefined) update.address = invoice.address;
  if (invoice.state !== undefined) update.state = invoice.state;
  if (invoice.city !== undefined) update.city = invoice.city;
  if (invoice.pincode !== undefined) update.pincode = invoice.pincode;
  if (invoice.estimatedAmount !== undefined) 
    update.estimated_amount = invoice.estimatedAmount ? parseFloat(invoice.estimatedAmount) : null;
  if (invoice.billableWarranty !== undefined) update.billable_warranty = invoice.billableWarranty;
  if (invoice.showRemarks !== undefined) update.show_remarks = invoice.showRemarks;
  if (invoice.remarks !== undefined) update.remarks = invoice.remarks;
  if (invoice.expectedDelivery !== undefined) update.expected_delivery = invoice.expectedDelivery;
  if (invoice.inspectionFee !== undefined) 
    update.inspection_fee = invoice.inspectionFee ? parseFloat(invoice.inspectionFee) : 500;
  
  return update;
}

// Convert used part to DB insert format
function convertUsedPartToDbInsert(part: Omit<UsedPart, 'id'>, invoiceId: string): UsedPartInsert {
  return {
    invoice_id: invoiceId,
    item_id: part.itemId.startsWith('custom-') ? null : part.itemId,
    name: part.name,
    quantity: part.quantity,
    price: parseFloat(part.price || '0'),
    manufacturer: part.manufacturer || null,
    model: part.model || null,
    serial_no: part.serialNo || null
  };
}

// Convert comment to DB insert format
function convertCommentToDbInsert(comment: Omit<Comment, 'id'>, invoiceId: string): CommentInsert {
  return {
    invoice_id: invoiceId,
    text: comment.text,
    author: comment.author,
    type: comment.type
  };
}

export function useSupabaseInvoices() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch invoices with related data
  const {
    data: invoices = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['invoices'],
    queryFn: async () => {
      // Fetch all invoices
      const { data: invoicesData, error: invoicesError } = await supabase
        .from('invoices')
        .select('*')
        .order('date', { ascending: false });

      if (invoicesError) {
        console.error('Error fetching invoices:', invoicesError);
        throw invoicesError;
      }

      // Fetch all used parts
      const { data: usedPartsData, error: usedPartsError } = await supabase
        .from('used_parts')
        .select('*');

      if (usedPartsError) {
        console.error('Error fetching used parts:', usedPartsError);
        throw usedPartsError;
      }

      // Fetch all comments
      const { data: commentsData, error: commentsError } = await supabase
        .from('comments')
        .select('*')
        .is('service_id', null); // Only get invoice comments

      if (commentsError) {
        console.error('Error fetching comments:', commentsError);
        throw commentsError;
      }

      // Map the data together
      return invoicesData.map(invoice => {
        const invoiceUsedParts = usedPartsData.filter(part => part.invoice_id === invoice.id);
        const invoiceComments = commentsData.filter(comment => comment.invoice_id === invoice.id);
        return convertFromDb(invoice, invoiceUsedParts, invoiceComments);
      });
    }
  });

  // Add invoice mutation
  const addInvoiceMutation = useMutation({
    mutationFn: async (invoiceData: Omit<Invoice, 'id' | 'created_at'>) => {
      // First insert the invoice
      const { data: newInvoice, error: invoiceError } = await supabase
        .from('invoices')
        .insert(convertToDbInsert(invoiceData))
        .select()
        .single();

      if (invoiceError) {
        console.error('Error adding invoice:', invoiceError);
        throw invoiceError;
      }

      const invoiceId = newInvoice.id;

      // Then insert used parts if any
      if (invoiceData.usedParts && invoiceData.usedParts.length > 0) {
        const usedPartsToInsert = invoiceData.usedParts.map(part => 
          convertUsedPartToDbInsert(part, invoiceId)
        );

        const { error: partsError } = await supabase
          .from('used_parts')
          .insert(usedPartsToInsert);

        if (partsError) {
          console.error('Error adding used parts:', partsError);
          throw partsError;
        }
      }

      // Then insert comments if any
      if (invoiceData.comments && invoiceData.comments.length > 0) {
        const commentsToInsert = invoiceData.comments.map(comment => 
          convertCommentToDbInsert(comment, invoiceId)
        );

        const { error: commentsError } = await supabase
          .from('comments')
          .insert(commentsToInsert);

        if (commentsError) {
          console.error('Error adding comments:', commentsError);
          throw commentsError;
        }
      }

      // Fetch the complete invoice with related data
      const { data: usedParts } = await supabase
        .from('used_parts')
        .select('*')
        .eq('invoice_id', invoiceId);

      const { data: comments } = await supabase
        .from('comments')
        .select('*')
        .eq('invoice_id', invoiceId);

      return convertFromDb(newInvoice, usedParts || [], comments || []);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: 'Success',
        description: 'Invoice added successfully.'
      });
    },
    onError: (error) => {
      console.error('Add invoice error:', error);
      toast({
        title: 'Error',
        description: 'Failed to add invoice. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update invoice mutation
  const updateInvoiceMutation = useMutation({
    mutationFn: async ({ id, invoiceData }: { id: string; invoiceData: Partial<Invoice> }) => {
      // First update the invoice
      const { data: updatedInvoice, error: invoiceError } = await supabase
        .from('invoices')
        .update(convertToDbUpdate(invoiceData))
        .eq('id', id)
        .select()
        .single();

      if (invoiceError) {
        console.error('Error updating invoice:', invoiceError);
        throw invoiceError;
      }

      // Handle used parts if provided
      if (invoiceData.usedParts) {
        // Delete existing parts
        const { error: deletePartsError } = await supabase
          .from('used_parts')
          .delete()
          .eq('invoice_id', id);

        if (deletePartsError) {
          console.error('Error deleting existing parts:', deletePartsError);
          throw deletePartsError;
        }

        // Insert new parts
        if (invoiceData.usedParts.length > 0) {
          const usedPartsToInsert = invoiceData.usedParts.map(part => 
            convertUsedPartToDbInsert(part, id)
          );

          const { error: insertPartsError } = await supabase
            .from('used_parts')
            .insert(usedPartsToInsert);

          if (insertPartsError) {
            console.error('Error adding updated parts:', insertPartsError);
            throw insertPartsError;
          }
        }
      }

      // Handle comments if provided
      if (invoiceData.comments) {
        // Get existing comments
        const { data: existingComments } = await supabase
          .from('comments')
          .select('id')
          .eq('invoice_id', id);

        const existingIds = new Set(existingComments?.map(c => c.id) || []);
        
        // Find new comments to insert
        const newComments = invoiceData.comments.filter(c => !c.id || !existingIds.has(c.id));
        
        // Insert new comments
        if (newComments.length > 0) {
          const commentsToInsert = newComments.map(comment => 
            convertCommentToDbInsert(comment, id)
          );

          const { error: commentsError } = await supabase
            .from('comments')
            .insert(commentsToInsert);

          if (commentsError) {
            console.error('Error adding new comments:', commentsError);
            throw commentsError;
          }
        }
      }

      // Fetch the complete updated invoice with related data
      const { data: usedParts } = await supabase
        .from('used_parts')
        .select('*')
        .eq('invoice_id', id);

      const { data: comments } = await supabase
        .from('comments')
        .select('*')
        .eq('invoice_id', id);

      return convertFromDb(updatedInvoice, usedParts || [], comments || []);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: 'Success',
        description: 'Invoice updated successfully.'
      });
    },
    onError: (error) => {
      console.error('Update invoice error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update invoice. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Delete invoice mutation
  const deleteInvoiceMutation = useMutation({
    mutationFn: async (id: string) => {
      // Delete the invoice (cascade will handle related records)
      const { error } = await supabase
        .from('invoices')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting invoice:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['invoices'] });
      toast({
        title: 'Success',
        description: 'Invoice deleted successfully.'
      });
    },
    onError: (error) => {
      console.error('Delete invoice error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete invoice. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Get today's sales
  const getTodaySales = () => {
    const today = new Date().toISOString().split('T')[0];
    return invoices
      .filter(inv => inv.date === today && inv.status === 'Paid')
      .reduce((sum, inv) => sum + parseFloat(inv.amount || '0'), 0);
  };

  return {
    invoices,
    isLoading,
    error,
    addInvoice: (invoiceData: Omit<Invoice, 'id' | 'created_at'>) => 
      addInvoiceMutation.mutateAsync(invoiceData),
    updateInvoice: (id: string, invoiceData: Partial<Invoice>) => 
      updateInvoiceMutation.mutateAsync({ id, invoiceData }),
    deleteInvoice: (id: string) => 
      deleteInvoiceMutation.mutateAsync(id),
    getTodaySales
  };
}
