
import { useToast } from "@/hooks/use-toast";

export function useInvoiceActions() {
  const { toast } = useToast();

  const handleMailInvoice = (invoice: any) => {
    const subject = `Invoice ${invoice.id} - ${invoice.customer}`;
    const body = `Dear ${invoice.customer},

Please find attached your invoice.

Invoice ID: ${invoice.id}
Device: ${invoice.device || 'N/A'}
Amount: ${invoice.amount}

Thank you for your business!

Best regards,
Service Team`;
    
    const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.open(mailtoLink);
    
    toast({
      title: "Email Client Opened",
      description: "Your default email client has been opened with the invoice details.",
    });
  };

  const handleDownloadInvoice = (invoice: any) => {
    const invoiceText = `
INVOICE: ${invoice.id}
Customer: ${invoice.customer}
Date: ${invoice.date}
Device: ${invoice.device || 'N/A'}
Issue: ${invoice.issue || 'N/A'}
Amount: ${invoice.amount}
Status: ${invoice.status}

Parts Used:
${invoice.usedParts?.map((part: any) => `- ${part.name} x${part.quantity}`).join('\n') || 'None'}

Additional Expenses:
${invoice.expenses?.map((expense: any) => `- ${expense.description}: ${expense.amount}`).join('\n') || 'None'}
    `;
    
    const blob = new Blob([invoiceText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Invoice-${invoice.id}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Invoice Downloaded",
      description: "Invoice has been downloaded successfully.",
    });
  };

  return {
    handleMailInvoice,
    handleDownloadInvoice
  };
}
