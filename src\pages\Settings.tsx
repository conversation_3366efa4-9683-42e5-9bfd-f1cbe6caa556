
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Settings as SettingsIcon, Bell, Users, Shield, Database } from "lucide-react";

export default function Settings() {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-3">
        <SettingsIcon className="h-8 w-8 text-orange-500" />
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Settings Navigation */}
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-0">
              <nav className="space-y-1">
                <a href="#" className="flex items-center px-4 py-3 text-sm font-medium text-orange-600 bg-orange-50 border-r-2 border-orange-500">
                  <SettingsIcon className="mr-3 h-5 w-5" />
                  Appearance
                </a>
                <a href="#" className="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                  <Bell className="mr-3 h-5 w-5" />
                  Notifications
                </a>
                <a href="#" className="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                  <SettingsIcon className="mr-3 h-5 w-5" />
                  Dashboard
                </a>
                <a href="#" className="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                  <Database className="mr-3 h-5 w-5" />
                  Inventory
                </a>
                <a href="#" className="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                  <Users className="mr-3 h-5 w-5" />
                  Customers
                </a>
                <a href="#" className="flex items-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50">
                  <Shield className="mr-3 h-5 w-5" />
                  Security
                </a>
              </nav>
            </CardContent>
          </Card>
        </div>

        {/* Settings Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Appearance Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">Appearance</CardTitle>
              <p className="text-sm text-gray-600">Customize the look and feel of the application.</p>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label className="text-sm font-medium text-gray-700">Theme</Label>
                <div className="mt-2 space-y-3">
                  <div className="flex items-center space-x-3">
                    <input type="radio" id="light" name="theme" className="text-orange-600" />
                    <Label htmlFor="light" className="text-sm text-gray-700">Light</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <input type="radio" id="dark" name="theme" className="text-orange-600" />
                    <Label htmlFor="dark" className="text-sm text-gray-700">Dark</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <input type="radio" id="system" name="theme" className="text-orange-600" defaultChecked />
                    <Label htmlFor="system" className="text-sm text-gray-700">System</Label>
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-2">Select the theme for the application.</p>
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium text-gray-700">Density</Label>
                <select className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                  <option value="comfortable">Comfortable</option>
                  <option value="compact">Compact</option>
                  <option value="spacious">Spacious</option>
                </select>
                <p className="text-xs text-gray-500 mt-2">Control the spacing between elements.</p>
              </div>

              <Separator />

              <div>
                <Label className="text-sm font-medium text-gray-700">Font Size</Label>
                <select className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                  <option value="small">Small</option>
                  <option value="default">Default</option>
                  <option value="large">Large</option>
                </select>
                <p className="text-xs text-gray-500 mt-2">Adjust the size of text throughout the application.</p>
              </div>
            </CardContent>
          </Card>

          {/* Business Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">Business Information</CardTitle>
              <p className="text-sm text-gray-600">Update your business details for invoices and reports.</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="businessName" className="text-sm font-medium text-gray-700">Business Name</Label>
                  <Input 
                    id="businessName" 
                    defaultValue="LaptopPro Store" 
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="taxId" className="text-sm font-medium text-gray-700">Tax ID</Label>
                  <Input 
                    id="taxId" 
                    defaultValue="***********" 
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="address" className="text-sm font-medium text-gray-700">Business Address</Label>
                <Input 
                  id="address" 
                  defaultValue="123 Tech Street, Austin, TX 78701" 
                  className="mt-1"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone" className="text-sm font-medium text-gray-700">Phone</Label>
                  <Input 
                    id="phone" 
                    defaultValue="+****************" 
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email</Label>
                  <Input 
                    id="email" 
                    defaultValue="<EMAIL>" 
                    type="email" 
                    className="mt-1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">Notifications</CardTitle>
              <p className="text-sm text-gray-600">Configure how you receive notifications.</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Email Notifications</Label>
                  <p className="text-xs text-gray-500">Receive updates via email</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">New Order Alerts</Label>
                  <p className="text-xs text-gray-500">Get notified when new orders are placed</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Low Stock Warnings</Label>
                  <p className="text-xs text-gray-500">Alert when inventory is running low</p>
                </div>
                <Switch defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Service Reminders</Label>
                  <p className="text-xs text-gray-500">Reminders for pending service requests</p>
                </div>
                <Switch />
              </div>
            </CardContent>
          </Card>

          {/* Save Changes */}
          <div className="flex justify-end">
            <Button className="bg-orange-500 hover:bg-orange-600 text-white">
              Save changes
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
