import { createContext, useContext, ReactNode } from 'react';
import { useSupabaseCustomers } from '@/hooks/useSupabaseCustomers';
import { useSupabaseInvoices } from '@/hooks/useSupabaseInvoices';
import { useSupabaseInventory } from '@/hooks/useSupabaseInventory';
import { useSupabaseServices } from '@/hooks/useSupabaseServices';
import { useSupabaseExpenses } from '@/hooks/useSupabaseExpenses';

// Create the context
const SupabaseAppDataContext = createContext<any>(null);

// Provider component
export function SupabaseAppDataProvider({ children }: { children: ReactNode }) {
  const customers = useSupabaseCustomers();
  const invoices = useSupabaseInvoices();
  const inventory = useSupabaseInventory();
  const services = useSupabaseServices();
  const expenses = useSupabaseExpenses();

  const value = {
    customers,
    invoices,
    inventory,
    services,
    expenses,
  };

  return (
    <SupabaseAppDataContext.Provider value={value}>
      {children}
    </SupabaseAppDataContext.Provider>
  );
}

// Hook to use the context
export function useSupabaseAppData() {
  const context = useContext(SupabaseAppDataContext);
  if (!context) {
    throw new Error('useSupabaseAppData must be used within a SupabaseAppDataProvider');
  }
  return context;
}
