import { createContext, useContext, ReactNode } from 'react';
import { useSupabaseCustomers } from '@/hooks/useSupabaseCustomers';
import { useSupabaseInvoices } from '@/hooks/useSupabaseInvoices';
import { useSupabaseInventory } from '@/hooks/useSupabaseInventory';
import { useSupabaseServices } from '@/hooks/useSupabaseServices';
import { useSupabaseExpenses } from '@/hooks/useSupabaseExpenses';

// Create the context
const SupabaseAppDataContext = createContext<any>(null);

// Provider component
export function SupabaseAppDataProvider({ children }: { children: ReactNode }) {
  const customersHook = useSupabaseCustomers();
  const invoicesHook = useSupabaseInvoices();
  const inventoryHook = useSupabaseInventory();
  const servicesHook = useSupabaseServices();
  const expensesHook = useSupabaseExpenses();

  const value = {
    // Data arrays
    customers: customersHook.customers || [],
    invoices: invoicesHook.invoices || [],
    inventory: inventoryHook.inventory || [],
    services: servicesHook.services || [],
    expenses: expensesHook.expenses || [],

    // Loading states
    isLoading: customersHook.isLoading || invoicesHook.isLoading ||
               inventoryHook.isLoading || servicesHook.isLoading ||
               expensesHook.isLoading,

    // Helper functions
    getLowStockItems: inventoryHook.getLowStockItems || (() => []),
    getTodaySales: invoicesHook.getTodaySales || (() => 0),
    getTodaysExpenses: expensesHook.getTodaysExpenses || (() => 0),
    getPartsUsedToday: inventoryHook.getPartsUsedToday || (() => []),

    // Mutation functions
    addCustomer: customersHook.addCustomer,
    updateCustomer: customersHook.updateCustomer,
    deleteCustomer: customersHook.deleteCustomer,
    addInvoice: invoicesHook.addInvoice,
    updateInvoice: invoicesHook.updateInvoice,
    deleteInvoice: invoicesHook.deleteInvoice,
    addService: servicesHook.addService,
    updateService: servicesHook.updateService,
    deleteService: servicesHook.deleteService,
    addInventoryItem: inventoryHook.addInventoryItem,
    updateInventoryItem: inventoryHook.updateInventoryItem,
    deleteInventoryItem: inventoryHook.deleteInventoryItem,
    addExpense: expensesHook.addExpense,
    updateExpense: expensesHook.updateExpense,
    deleteExpense: expensesHook.deleteExpense,
  };

  return (
    <SupabaseAppDataContext.Provider value={value}>
      {children}
    </SupabaseAppDataContext.Provider>
  );
}

// Hook to use the context
export function useSupabaseAppData() {
  const context = useContext(SupabaseAppDataContext);
  if (!context) {
    throw new Error('useSupabaseAppData must be used within a SupabaseAppDataProvider');
  }
  return context;
}
