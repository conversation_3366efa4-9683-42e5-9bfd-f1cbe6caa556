
import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>, CheckCircle, AlertTriangle } from "lucide-react";

interface ServiceStatsProps {
  totalServices: number;
  pendingServices: number;
  inProgressServices: number;
  completedServices: number;
}

export function ServiceStats({
  totalServices,
  pendingServices,
  inProgressServices,
  completedServices
}: ServiceStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">All Services</CardTitle>
          <Wrench className="h-5 w-5 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">{totalServices}</div>
          <p className="text-xs text-gray-500 mt-1">Total service requests</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Pending</CardTitle>
          <AlertTriangle className="h-5 w-5 text-yellow-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{pendingServices}</div>
          <p className="text-xs text-gray-500 mt-1">Awaiting attention</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">In Progress</CardTitle>
          <Clock className="h-5 w-5 text-blue-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">{inProgressServices}</div>
          <p className="text-xs text-gray-500 mt-1">Currently being worked on</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-gray-600">Completed</CardTitle>
          <CheckCircle className="h-5 w-5 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{completedServices}</div>
          <p className="text-xs text-gray-500 mt-1">Successfully finished</p>
        </CardContent>
      </Card>
    </div>
  );
}
