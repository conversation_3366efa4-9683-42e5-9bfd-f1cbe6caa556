import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Edit, Mail, X, Wrench, Clock, CheckCircle, AlertTriangle, Eye, Download, Trash } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ServiceDialog } from "@/components/dialogs/ServiceDialog";
import { InvoiceDialog } from "@/components/dialogs/InvoiceDialog";
import { InvoiceReadOnlyDialog } from "@/components/dialogs/InvoiceReadOnlyDialog";
import { ServiceStats } from "@/components/services/ServiceStats";
import { ServiceFilters } from "@/components/services/ServiceFilters";
import { useToast } from "@/hooks/use-toast";
import { useAppData } from "@/contexts/AppDataContext";

export default function Services() {
  const { services, invoices, addService, updateService, deleteService, addInvoice, updateInvoice, deleteInvoice } = useAppData();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("All Services");
  const [isServiceDialogOpen, setIsServiceDialogOpen] = useState(false);
  const [isInvoiceDialogOpen, setIsInvoiceDialogOpen] = useState(false);
  const [isInvoiceViewDialogOpen, setIsInvoiceViewDialogOpen] = useState(false);
  const [editingService, setEditingService] = useState(undefined);
  const [editingInvoice, setEditingInvoice] = useState(undefined);
  const [viewingInvoice, setViewingInvoice] = useState(undefined);
  const { toast } = useToast();

  // Calculate service statistics
  const totalServices = services.length;
  const pendingServices = services.filter(s => s.status === 'Pending').length;
  const inProgressServices = services.filter(s => s.status === 'In Progress').length;
  const completedServices = services.filter(s => s.status === 'Completed').length;

  const filteredServices = services.filter(service => {
    const matchesSearch = service.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.device.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "All Services" || service.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleAddService = () => {
    setEditingService(undefined);
    setIsServiceDialogOpen(true);
  };

  const handleEditService = (service: any) => {
    setEditingService(service);
    setIsServiceDialogOpen(true);
  };

  const handleDeleteService = (serviceId: string) => {
    deleteService(serviceId);
    // Also delete associated invoice if exists
    const associatedInvoice = invoices.find(inv => inv.id === serviceId);
    if (associatedInvoice) {
      deleteInvoice(serviceId);
    }
    toast({
      title: "Service Deleted",
      description: "Service request and associated invoice have been successfully deleted.",
    });
  };

  const handleSaveService = (serviceData: any) => {
    console.log('Saving service:', serviceData);
    if (editingService) {
      updateService(editingService.id, serviceData);
      toast({
        title: "Service Updated",
        description: "Service request has been successfully updated.",
      });
    } else {
      const newService = addService(serviceData);
      console.log('New service created:', newService);
      toast({
        title: "Service Created",
        description: "New service request has been successfully created.",
      });
    }
  };

  const handleViewInvoice = (service: any) => {
    const invoice = invoices.find(inv => inv.id === service.id);
    if (invoice) {
      setViewingInvoice(invoice);
      setIsInvoiceViewDialogOpen(true);
    } else {
      toast({
        title: "No Invoice Found",
        description: "No invoice found for this service request.",
        variant: "destructive",
      });
    }
  };

  const handleEditInvoice = (service: any) => {
    const invoice = invoices.find(inv => inv.id === service.id);
    if (invoice) {
      setEditingInvoice(invoice);
      setIsInvoiceDialogOpen(true);
    } else {
      // Create new invoice for this service
      const newInvoice = {
        customer: service.customer,
        date: new Date().toISOString().split('T')[0],
        amount: service.cost,
        status: "Pending",
        device: service.device,
        issue: service.issue,
        usedParts: [],
        expenses: [],
        comments: []
      };
      setEditingInvoice(newInvoice);
      setIsInvoiceDialogOpen(true);
    }
  };

  const handleDownloadInvoice = (service: any) => {
    const invoice = invoices.find(inv => inv.id === service.id);
    if (invoice) {
      // Create a simple text representation of the invoice
      const invoiceText = `
INVOICE: ${invoice.id}
Customer: ${invoice.customer}
Date: ${invoice.date}
Device: ${invoice.device || 'N/A'}
Issue: ${invoice.issue || 'N/A'}
Amount: ${invoice.amount}
Status: ${invoice.status}
      `;
      
      const blob = new Blob([invoiceText], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Invoice-${invoice.id}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Invoice Downloaded",
        description: "Invoice has been downloaded successfully.",
      });
    } else {
      toast({
        title: "No Invoice Found",
        description: "No invoice found for this service request.",
        variant: "destructive",
      });
    }
  };

  const handleMailInvoice = (service: any) => {
    const invoice = invoices.find(inv => inv.id === service.id);
    if (invoice) {
      const subject = `Invoice ${invoice.id} - ${service.customer}`;
      const body = `Dear ${service.customer},

Please find attached your invoice for the service request.

Invoice ID: ${invoice.id}
Device: ${invoice.device || 'N/A'}
Issue: ${invoice.issue || 'N/A'}
Amount: ${invoice.amount}

Thank you for your business!

Best regards,
Service Team`;
      
      const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(mailtoLink);
      
      toast({
        title: "Email Client Opened",
        description: "Your default email client has been opened with the invoice details.",
      });
    } else {
      toast({
        title: "No Invoice Found",
        description: "No invoice found for this service request.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteInvoice = (service: any) => {
    const invoice = invoices.find(inv => inv.id === service.id);
    if (invoice) {
      deleteInvoice(service.id);
      toast({
        title: "Invoice Deleted",
        description: "Invoice has been successfully deleted.",
      });
    } else {
      toast({
        title: "No Invoice Found",
        description: "No invoice found for this service request.",
        variant: "destructive",
      });
    }
  };

  const handleSaveInvoice = (invoiceData: any) => {
    console.log('Saving invoice from services:', invoiceData);
    if (editingInvoice) {
      if (editingInvoice.id) {
        updateInvoice(editingInvoice.id, invoiceData);
        toast({
          title: "Invoice Updated",
          description: "Final invoice has been successfully updated.",
        });
      } else {
        // Create new invoice with service ID
        const service = services.find(s => s.customer === invoiceData.customer);
        if (service) {
          const newInvoice = {
            ...invoiceData,
            id: service.id // Use service ID for invoice
          };
          const createdInvoice = addInvoice(newInvoice);
          console.log('New invoice created from services:', createdInvoice);
          toast({
            title: "Invoice Created",
            description: "Final invoice has been successfully created.",
          });
        }
      }
    }
    setIsInvoiceDialogOpen(false);
    setEditingInvoice(undefined);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-100 text-green-800';
      case 'In Progress':
        return 'bg-blue-100 text-blue-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInvoiceForService = (serviceId: string) => {
    return invoices.find(inv => inv.id === serviceId);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Service Management</h1>
        <Button onClick={handleAddService} className="bg-orange-500 hover:bg-orange-600 text-white">
          <Plus className="w-4 h-4 mr-2" />
          New Service Request
        </Button>
      </div>

      <ServiceStats
        totalServices={totalServices}
        pendingServices={pendingServices}
        inProgressServices={inProgressServices}
        completedServices={completedServices}
      />

      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">Service Requests & Final Invoices</CardTitle>
          <p className="text-sm text-gray-600">Manage laptop repair services and finalize invoices</p>
        </CardHeader>
        <CardContent>
          <ServiceFilters
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
          />

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Device / Issue</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Technician</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead>Invoice Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredServices.map((service) => {
                const associatedInvoice = getInvoiceForService(service.id);
                return (
                  <TableRow key={service.id}>
                    <TableCell className="font-medium">{service.id}</TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{service.customer}</div>
                        <div className="text-sm text-gray-500">{service.phone}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{service.device}</div>
                        <div className="text-sm text-gray-500">{service.issue}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(service.status)}`}>
                        {service.status}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className={service.technician === 'Not assigned' ? 'text-gray-500' : 'text-gray-900'}>
                        {service.technician}
                      </span>
                    </TableCell>
                    <TableCell className="font-medium">{service.cost}</TableCell>
                    <TableCell>
                      {associatedInvoice ? (
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          associatedInvoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                          associatedInvoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {associatedInvoice.status}
                        </span>
                      ) : (
                        <span className="text-gray-500 text-sm">No Invoice</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Button variant="ghost" size="sm" onClick={() => handleEditService(service)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleViewInvoice(service)}>
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleEditInvoice(service)}>
                          <Edit className="h-4 w-4 text-blue-600" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleDownloadInvoice(service)}>
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" onClick={() => handleMailInvoice(service)}>
                          <Mail className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleDeleteService(service.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <ServiceDialog
        isOpen={isServiceDialogOpen}
        onClose={() => setIsServiceDialogOpen(false)}
        service={editingService}
        onSave={handleSaveService}
      />

      <InvoiceDialog
        isOpen={isInvoiceDialogOpen}
        onClose={() => {
          setIsInvoiceDialogOpen(false);
          setEditingInvoice(undefined);
        }}
        invoice={editingInvoice}
        onSave={handleSaveInvoice}
      />

      <InvoiceReadOnlyDialog
        isOpen={isInvoiceViewDialogOpen}
        onClose={() => {
          setIsInvoiceViewDialogOpen(false);
          setViewingInvoice(undefined);
        }}
        invoice={viewingInvoice}
      />
    </div>
  );
}
