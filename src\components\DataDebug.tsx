import { useSupabaseAppData } from "@/contexts/SupabaseAppDataContext";

export function DataDebug() {
  const {
    customers,
    invoices,
    inventory,
    services,
    expenses,
    isLoading
  } = useSupabaseAppData();

  return (
    <div className="fixed bottom-4 right-4 bg-white border border-gray-300 p-4 rounded shadow-lg text-sm z-50 max-w-md">
      <h3 className="font-bold mb-2">Data Debug</h3>
      <div className="space-y-1">
        <div>Loading: {isLoading ? 'true' : 'false'}</div>
        <div>Customers: {customers?.length || 0}</div>
        <div>Invoices: {invoices?.length || 0}</div>
        <div>Services: {services?.length || 0}</div>
        <div>Inventory: {inventory?.length || 0}</div>
        <div>Expenses: {expenses?.length || 0}</div>
      </div>
      {customers?.length > 0 && (
        <div className="mt-2">
          <div className="text-xs text-gray-600">First customer:</div>
          <div className="text-xs">{customers[0].name}</div>
        </div>
      )}
    </div>
  );
}
