import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';
import { Constants } from '@/integrations/supabase/types';
import { Comment } from './useSupabaseInvoices';

type ServiceRow = Database['public']['Tables']['services']['Row'];
type ServiceInsert = Database['public']['Tables']['services']['Insert'];
type ServiceUpdate = Database['public']['Tables']['services']['Update'];
type CommentRow = Database['public']['Tables']['comments']['Row'];
type CommentInsert = Database['public']['Tables']['comments']['Insert'];

export interface Service {
  id: string;
  customer: string;
  phone: string;
  device: string;
  issue: string;
  status: typeof Constants.public.Enums.service_status[number];
  technician: string;
  cost: string;
  invoiceId: string | null;
  comments: Comment[];
  created_at: string;
}

// Convert from DB format to app format
function convertFromDb(dbService: ServiceRow, comments: CommentRow[] = []): Service {
  return {
    id: dbService.id,
    customer: dbService.customer_name,
    phone: dbService.phone || '',
    device: dbService.device || '',
    issue: dbService.issue || '',
    status: dbService.status,
    technician: dbService.technician || '',
    cost: dbService.cost?.toString() || '0',
    invoiceId: dbService.invoice_id,
    comments: comments.map(comment => ({
      id: comment.id,
      text: comment.text,
      author: comment.author,
      timestamp: comment.created_at,
      type: comment.type
    })),
    created_at: dbService.created_at
  };
}

// Convert to DB insert format
function convertToDbInsert(service: Omit<Service, 'id' | 'comments' | 'created_at'>): ServiceInsert {
  return {
    customer_name: service.customer,
    phone: service.phone,
    device: service.device,
    issue: service.issue,
    status: service.status,
    technician: service.technician,
    cost: service.cost ? parseFloat(service.cost) : null,
    invoice_id: service.invoiceId
  };
}

// Convert to DB update format
function convertToDbUpdate(service: Partial<Service>): ServiceUpdate {
  const update: ServiceUpdate = {};
  
  if (service.customer !== undefined) update.customer_name = service.customer;
  if (service.phone !== undefined) update.phone = service.phone;
  if (service.device !== undefined) update.device = service.device;
  if (service.issue !== undefined) update.issue = service.issue;
  if (service.status !== undefined) update.status = service.status;
  if (service.technician !== undefined) update.technician = service.technician;
  if (service.cost !== undefined) update.cost = service.cost ? parseFloat(service.cost) : null;
  if (service.invoiceId !== undefined) update.invoice_id = service.invoiceId;
  
  return update;
}

// Convert comment to DB insert format
function convertCommentToDbInsert(comment: Omit<Comment, 'id'>, serviceId: string): CommentInsert {
  return {
    service_id: serviceId,
    text: comment.text,
    author: comment.author,
    type: comment.type
  };
}

export function useSupabaseServices() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch services with related data
  const {
    data: services = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['services'],
    queryFn: async () => {
      // Fetch all services
      const { data: servicesData, error: servicesError } = await supabase
        .from('services')
        .select('*')
        .order('created_at', { ascending: false });

      if (servicesError) {
        console.error('Error fetching services:', servicesError);
        throw servicesError;
      }

      // Fetch comments for each service
      const { data: commentsData, error: commentsError } = await supabase
        .from('comments')
        .select('*');

      if (commentsError) {
        console.error('Error fetching comments:', commentsError);
        throw commentsError;
      }

      // Group comments by service_id
      const commentsByServiceId: { [key: string]: CommentRow[] } = commentsData.reduce((acc, comment) => {
        if (!acc[comment.service_id!]) {
          acc[comment.service_id!] = [];
        }
        acc[comment.service_id!].push(comment);
        return acc;
      }, {} as { [key: string]: CommentRow[] });

      // Convert services to app format
      return servicesData.map(service => convertFromDb(service, commentsByServiceId[service.id!]));
    }
  });

  // Add service mutation
  const addServiceMutation = useMutation({
    mutationFn: async (serviceData: Omit<Service, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('services')
        .insert(convertToDbInsert(serviceData))
        .select()
        .single();

      if (error) {
        console.error('Error adding service:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: (newService) => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      toast({
        title: 'Success',
        description: `Service ${newService.name} added successfully.`
      });
    },
    onError: (error) => {
      console.error('Add service error:', error);
      toast({
        title: 'Error',
        description: 'Failed to add service. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update service mutation
  const updateServiceMutation = useMutation({
    mutationFn: async ({ id, serviceData }: { id: string; serviceData: Partial<Service> }) => {
      const { data, error } = await supabase
        .from('services')
        .update(convertToDbUpdate(serviceData))
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating service:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: (updatedService) => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      toast({
        title: 'Success',
        description: `Service ${updatedService.name} updated successfully.`
      });
    },
    onError: (error) => {
      console.error('Update service error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update service. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Delete service mutation
  const deleteServiceMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('services')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting service:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['services'] });
      toast({
        title: 'Success',
        description: 'Service deleted successfully.'
      });
    },
    onError: (error) => {
      console.error('Delete service error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete service. Please try again.',
        variant: 'destructive'
      });
    }
  });

  return {
    services,
    isLoading,
    error,
    addService: (serviceData: Omit<Service, 'id' | 'created_at'>) => addServiceMutation.mutateAsync(serviceData),
    updateService: (id: string, serviceData: Partial<Service>) => 
      updateServiceMutation.mutateAsync({ id, serviceData }),
    deleteService: (id: string) => deleteServiceMutation.mutateAsync(id),
  };
}
