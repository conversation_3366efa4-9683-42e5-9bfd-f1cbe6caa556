
import { useState } from "react";
import { ExpensesSection } from "./ExpensesSection";
import type { ExpenseItem } from "../../../types";

interface ExpensesManagerProps {
  expenses: ExpenseItem[];
  onUpdateExpenses: (expenses: ExpenseItem[]) => void;
}

export function ExpensesManager({ expenses, onUpdateExpenses }: ExpensesManagerProps) {
  const [newExpense, setNewExpense] = useState<ExpenseItem>({ description: "", amount: "", category: "Parts" });

  const addExpenseToInvoice = () => {
    if (newExpense.description && newExpense.amount) {
      onUpdateExpenses([...expenses, { ...newExpense }]);
      setNewExpense({ description: "", amount: "", category: "Parts" });
    }
  };

  const removeExpenseFromInvoice = (index: number) => {
    onUpdateExpenses(expenses.filter((_, i) => i !== index));
  };

  return (
    <ExpensesSection
      expenses={expenses}
      newExpense={newExpense}
      setNewExpense={setNewExpense}
      onAddExpense={addExpenseToInvoice}
      onRemoveExpense={removeExpenseFromInvoice}
    />
  );
}
