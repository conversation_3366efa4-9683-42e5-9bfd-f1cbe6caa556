
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';

type CustomerRow = Database['public']['Tables']['customers']['Row'];
type CustomerInsert = Database['public']['Tables']['customers']['Insert'];
type CustomerUpdate = Database['public']['Tables']['customers']['Update'];

export interface Customer {
  id: string;
  name: string;
  email: string | null;
  phone: string;
  alternate_phone: string | null;
  address: string;
  state: string | null;
  city: string | null;
  pincode: string | null;
  purchases: number;
  services: number;
  total_spent: number;
  warranty: string | null;
  created_at: string;
}

// Convert from DB format to app format
function convertFromDb(dbCustomer: CustomerRow): Customer {
  return {
    id: dbCustomer.id,
    name: dbCustomer.name,
    email: dbCustomer.email,
    phone: dbCustomer.phone,
    alternate_phone: dbCustomer.alternate_phone,
    address: dbCustomer.address,
    state: dbCustomer.state,
    city: dbCustomer.city,
    pincode: dbCustomer.pincode,
    purchases: dbCustomer.purchases,
    services: dbCustomer.services,
    total_spent: dbCustomer.total_spent,
    warranty: dbCustomer.warranty,
    created_at: dbCustomer.created_at
  };
}

// Convert to DB insert format
function convertToDbInsert(customer: Omit<Customer, 'id' | 'purchases' | 'services' | 'total_spent' | 'created_at'>): CustomerInsert {
  return {
    name: customer.name,
    email: customer.email,
    phone: customer.phone,
    alternate_phone: customer.alternate_phone,
    address: customer.address,
    state: customer.state,
    city: customer.city,
    pincode: customer.pincode,
    warranty: customer.warranty
  };
}

// Convert to DB update format
function convertToDbUpdate(customer: Partial<Customer>): CustomerUpdate {
  const update: CustomerUpdate = {};
  
  if (customer.name !== undefined) update.name = customer.name;
  if (customer.email !== undefined) update.email = customer.email;
  if (customer.phone !== undefined) update.phone = customer.phone;
  if (customer.alternate_phone !== undefined) update.alternate_phone = customer.alternate_phone;
  if (customer.address !== undefined) update.address = customer.address;
  if (customer.state !== undefined) update.state = customer.state;
  if (customer.city !== undefined) update.city = customer.city;
  if (customer.pincode !== undefined) update.pincode = customer.pincode;
  if (customer.warranty !== undefined) update.warranty = customer.warranty;
  
  return update;
}

export function useSupabaseCustomers() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch customers
  const {
    data: customers = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['customers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching customers:', error);
        throw error;
      }

      return data.map(convertFromDb);
    }
  });

  // Add customer mutation
  const addCustomerMutation = useMutation({
    mutationFn: async (customerData: Omit<Customer, 'id' | 'purchases' | 'services' | 'total_spent' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('customers')
        .insert(convertToDbInsert(customerData))
        .select()
        .single();

      if (error) {
        console.error('Error adding customer:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: 'Success',
        description: 'Customer added successfully.'
      });
    },
    onError: (error) => {
      console.error('Add customer error:', error);
      toast({
        title: 'Error',
        description: 'Failed to add customer. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update customer mutation
  const updateCustomerMutation = useMutation({
    mutationFn: async ({ id, customerData }: { id: string; customerData: Partial<Customer> }) => {
      const { data, error } = await supabase
        .from('customers')
        .update(convertToDbUpdate(customerData))
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating customer:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: 'Success',
        description: 'Customer updated successfully.'
      });
    },
    onError: (error) => {
      console.error('Update customer error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update customer. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Delete customer mutation
  const deleteCustomerMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('customers')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting customer:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['customers'] });
      toast({
        title: 'Success',
        description: 'Customer deleted successfully.'
      });
    },
    onError: (error) => {
      console.error('Delete customer error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete customer. Please try again.',
        variant: 'destructive'
      });
    }
  });

  return {
    customers,
    isLoading,
    error,
    addCustomer: (customerData: Omit<Customer, 'id' | 'purchases' | 'services' | 'total_spent' | 'created_at'>) => 
      addCustomerMutation.mutateAsync(customerData),
    updateCustomer: (id: string, customerData: Partial<Customer>) => 
      updateCustomerMutation.mutateAsync({ id, customerData }),
    deleteCustomer: (id: string) => deleteCustomerMutation.mutateAsync(id),
  };
}
