
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { User } from "lucide-react";
import { useAppData } from "@/contexts/AppDataContext";
import { CustomerInformation } from "./customer/CustomerInformation";
import { CustomerStatistics } from "./customer/CustomerStatistics";
import { CustomerInvoiceHistory } from "./customer/CustomerInvoiceHistory";
import type { Customer } from "@/hooks/useSupabaseCustomers"; // Import from the same source

interface CustomerReadOnlyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer;
  onViewInvoice: (invoiceId: string) => void;
}

export function CustomerReadOnlyDialog({ 
  isOpen, 
  onClose, 
  customer, 
  onViewInvoice 
}: CustomerReadOnlyDialogProps) {
  const { invoices } = useAppData();

  if (!customer) return null;

  const customerInvoices = invoices.filter(inv => inv.customer === customer.name);
  const totalSpent = customerInvoices
    .filter(inv => inv.status === 'Paid')
    .reduce((sum, inv) => sum + parseFloat(inv.amount.replace(/[₹,]/g, '')), 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>View Customer Details - {customer.name}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            <CustomerInformation customer={customer} />
            <CustomerStatistics customerInvoices={customerInvoices} totalSpent={totalSpent} />
          </div>

          <div className="space-y-4">
            <CustomerInvoiceHistory customerInvoices={customerInvoices} onViewInvoice={onViewInvoice} />
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600">
            Print Customer Profile
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
