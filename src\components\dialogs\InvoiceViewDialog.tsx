
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, User, DollarSign, Package, MessageSquare } from "lucide-react";

interface Invoice {
  id?: string;
  customer: string;
  date: string;
  amount: string;
  status: string;
  device?: string;
  issue?: string;
  usedParts?: { itemId: string; quantity: number; name: string }[];
  expenses?: { description: string; amount: string; category: string }[];
  comments?: { text: string; author: string; timestamp: string; type: 'edit' | 'customer' | 'internal' }[];
}

interface InvoiceViewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: Invoice;
}

export function InvoiceViewDialog({ isOpen, onClose, invoice }: InvoiceViewDialogProps) {
  if (!invoice) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateTotal = () => {
    let total = parseFloat(invoice.amount.replace(/[₹,]/g, '')) || 0;
    
    if (invoice.expenses) {
      invoice.expenses.forEach(expense => {
        total += parseFloat(expense.amount.replace(/[₹,]/g, '')) || 0;
      });
    }
    
    return `₹${total.toLocaleString()}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Invoice Details - {invoice.id}</span>
            <Badge className={getStatusColor(invoice.status)}>
              {invoice.status}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Invoice Header */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <User className="w-4 h-4 text-gray-500" />
                <span className="font-medium">Customer</span>
              </div>
              <p className="text-lg font-semibold">{invoice.customer}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-500" />
                <span className="font-medium">Date</span>
              </div>
              <p className="text-lg">{formatDate(invoice.date)}</p>
            </div>
          </div>

          <Separator />

          {/* Service Details */}
          {(invoice.device || invoice.issue) && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Service Details</h3>
              <div className="grid grid-cols-1 gap-3">
                {invoice.device && (
                  <div>
                    <span className="font-medium text-gray-600">Device:</span>
                    <p className="text-gray-900">{invoice.device}</p>
                  </div>
                )}
                {invoice.issue && (
                  <div>
                    <span className="font-medium text-gray-600">Issue:</span>
                    <p className="text-gray-900">{invoice.issue}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Parts Used */}
          {invoice.usedParts && invoice.usedParts.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Package className="w-5 h-5" />
                <span>Parts Used</span>
              </h3>
              <div className="space-y-2">
                {invoice.usedParts.map((part, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span>{part.name}</span>
                    <span className="font-medium">Qty: {part.quantity}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Expenses */}
          {invoice.expenses && invoice.expenses.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Additional Expenses</h3>
              <div className="space-y-2">
                {invoice.expenses.map((expense, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <div>
                      <span className="font-medium">{expense.description}</span>
                      <span className="text-sm text-gray-500 ml-2">({expense.category})</span>
                    </div>
                    <span className="font-medium">{expense.amount}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Total Amount */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xl font-semibold">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-5 h-5 text-orange-500" />
                <span>Total Amount</span>
              </div>
              <span className="text-orange-600">{calculateTotal()}</span>
            </div>
          </div>

          {/* Comments Section */}
          {invoice.comments && invoice.comments.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <MessageSquare className="w-5 h-5" />
                <span>Edit History & Comments</span>
              </h3>
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {invoice.comments.map((comment, index) => (
                      <div key={index} className="border-l-4 border-orange-200 pl-4 py-2">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-sm">{comment.author}</span>
                            <Badge variant="outline" className="text-xs">
                              {comment.type}
                            </Badge>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(comment.timestamp).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700">{comment.text}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button className="bg-orange-500 hover:bg-orange-600">
              Print Invoice
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
