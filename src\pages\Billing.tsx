
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { InvoiceDialog } from "@/components/dialogs/InvoiceDialog";
import { InvoiceViewDialog } from "@/components/dialogs/InvoiceViewDialog";
import { InvoiceTable } from "@/components/billing/InvoiceTable";
import { RevenueStats } from "@/components/billing/RevenueStats";
import { Plus } from "lucide-react";
import { useSupabaseAppData } from "@/contexts/SupabaseAppDataContext";

export default function Billing() {
  const { 
    invoices, 
    customers, 
    addInvoice, 
    updateInvoice, 
    deleteInvoice,
    isLoading 
  } = useSupabaseAppData();
  
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any>();
  const [searchTerm, setSearchTerm] = useState("");

  const filteredInvoices = invoices.filter(invoice =>
    invoice.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invoice.id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invoice.device?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate revenue stats from invoices
  const paidInvoices = invoices.filter(inv => inv.status === 'Paid');
  const pendingInvoices = invoices.filter(inv => inv.status === 'Pending');
  
  const totalRevenue = paidInvoices.reduce((sum, inv) => {
    const amount = parseFloat(inv.amount.replace(/[₹,]/g, '')) || 0;
    return sum + amount;
  }, 0);
  
  const pendingRevenue = pendingInvoices.reduce((sum, inv) => {
    const amount = parseFloat(inv.amount.replace(/[₹,]/g, '')) || 0;
    return sum + amount;
  }, 0);

  const handleSaveInvoice = async (invoiceData: any) => {
    try {
      if (selectedInvoice?.id) {
        await updateInvoice(selectedInvoice.id, invoiceData);
      } else {
        await addInvoice(invoiceData);
      }
    } catch (error) {
      console.error('Error saving invoice:', error);
    }
  };

  const handleEditInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setIsDialogOpen(true);
  };

  const handleViewInvoice = (invoice: any) => {
    setSelectedInvoice(invoice);
    setIsViewDialogOpen(true);
  };

  const handleDeleteInvoice = async (invoiceId: string) => {
    try {
      await deleteInvoice(invoiceId);
    } catch (error) {
      console.error('Error deleting invoice:', error);
    }
  };

  const handleMailInvoice = (invoice: any) => {
    // Placeholder for mail functionality
    console.log('Mail invoice:', invoice);
  };

  const handleDownloadInvoice = (invoice: any) => {
    // Placeholder for download functionality
    console.log('Download invoice:', invoice);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Billing & Invoices</h1>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Billing & Invoices</h1>
          <p className="text-muted-foreground">Manage invoices and track payments</p>
        </div>
        <Button 
          onClick={() => {
            setSelectedInvoice(undefined);
            setIsDialogOpen(true);
          }}
          className="bg-orange-500 hover:bg-orange-600"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Invoice
        </Button>
      </div>

      <RevenueStats 
        totalRevenue={totalRevenue}
        pendingRevenue={pendingRevenue}
        totalInvoices={invoices.length}
        paidInvoices={paidInvoices.length}
        pendingInvoicesCount={pendingInvoices.length}
      />

      <Card>
        <CardHeader>
          <CardTitle>Invoices</CardTitle>
        </CardHeader>
        <CardContent>
          <InvoiceTable
            filteredInvoices={filteredInvoices}
            onViewInvoice={handleViewInvoice}
            onEditInvoice={handleEditInvoice}
            onMailInvoice={handleMailInvoice}
            onDownloadInvoice={handleDownloadInvoice}
            onDeleteInvoice={handleDeleteInvoice}
          />
        </CardContent>
      </Card>

      <InvoiceDialog
        isOpen={isDialogOpen}
        onClose={() => {
          setIsDialogOpen(false);
          setSelectedInvoice(undefined);
        }}
        invoice={selectedInvoice}
        onSave={handleSaveInvoice}
      />

      <InvoiceViewDialog
        isOpen={isViewDialogOpen}
        onClose={() => setIsViewDialogOpen(false)}
        invoice={selectedInvoice}
      />
    </div>
  );
}
