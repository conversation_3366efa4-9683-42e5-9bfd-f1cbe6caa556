
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Eye, Edit, Mail, Download, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface InvoiceTableProps {
  filteredInvoices: any[];
  onViewInvoice: (invoice: any) => void;
  onEditInvoice: (invoice: any) => void;
  onMailInvoice: (invoice: any) => void;
  onDownloadInvoice: (invoice: any) => void;
  onDeleteInvoice: (id: string) => void;
}

export function InvoiceTable({
  filteredInvoices,
  onViewInvoice,
  onEditInvoice,
  onMailInvoice,
  onDownloadInvoice,
  onDeleteInvoice
}: InvoiceTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Invoice</TableHead>
          <TableHead>Customer</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {filteredInvoices.map((invoice) => (
          <TableRow key={invoice.id}>
            <TableCell className="font-medium">{invoice.id}</TableCell>
            <TableCell>
              <div>
                <div className="font-medium">{invoice.customer}</div>
                {invoice.device && (
                  <div className="text-sm text-gray-500">{invoice.device}</div>
                )}
              </div>
            </TableCell>
            <TableCell>{invoice.date}</TableCell>
            <TableCell className="font-medium">{invoice.amount}</TableCell>
            <TableCell>
              <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                invoice.status === 'Paid' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {invoice.status}
              </span>
            </TableCell>
            <TableCell>
              <div className="flex items-center space-x-2">
                <Button variant="ghost" size="sm" onClick={() => onViewInvoice(invoice)}>
                  <Eye className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => onEditInvoice(invoice)}>
                  <Edit className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => onMailInvoice(invoice)}>
                  <Mail className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => onDownloadInvoice(invoice)}>
                  <Download className="h-4 w-4" />
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => onDeleteInvoice(invoice.id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
