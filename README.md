# Orangeware Billing Suite

A comprehensive billing and service management system built with React, TypeScript, and Supabase.

## Features

- **Customer Management**: Add, edit, and manage customer information
- **Service Tracking**: Track repair services and their status
- **Invoice Management**: Create and manage invoices
- **Inventory Management**: Track parts and inventory
- **Expense Tracking**: Monitor business expenses
- **Authentication**: Secure login system with Supabase Auth
- **Real-time Data**: Live updates using Supabase real-time features

## Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **UI Components**: Shadcn/ui + Radix UI + Tailwind CSS
- **Backend**: Supabase (Database + Auth + Real-time)
- **State Management**: TanStack Query (React Query)
- **Routing**: React Router DOM
- **Forms**: React Hook Form + Zod validation

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   - Copy `.env.example` to `.env`
   - Update the Supabase credentials in `.env`

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Open [http://localhost:8080](http://localhost:8080) in your browser

### Test Credentials

For testing purposes, use these credentials:
- **Email**: <EMAIL>
- **Password**: password123

## Project Structure

```
src/
├── components/          # Reusable UI components
├── contexts/           # React contexts (Supabase data provider)
├── hooks/              # Custom hooks (Supabase operations)
├── integrations/       # Supabase client and types
├── pages/              # Page components
├── lib/                # Utility functions
└── types.ts            # TypeScript type definitions
```

## Database Schema

The application uses the following main tables:
- `customers` - Customer information
- `services` - Service/repair records
- `invoices` - Invoice data
- `inventory_items` - Parts and inventory
- `expenses` - Business expenses
- `comments` - Comments for services and invoices

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Supabase Integration

This application is fully integrated with Supabase for:
- **Authentication**: Email/password login with session management
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Real-time**: Live updates across all data tables
- **API**: Auto-generated REST API from database schema

### Key Integration Files

- `src/integrations/supabase/client.ts` - Supabase client configuration
- `src/integrations/supabase/types.ts` - Auto-generated TypeScript types
- `src/hooks/useSupabaseAuth.tsx` - Authentication hook
- `src/hooks/useSupabase*.tsx` - Data operation hooks for each table
- `src/contexts/SupabaseAppDataContext.tsx` - Global data provider

## Environment Variables

The following environment variables are required:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Deployment

The application can be deployed to any static hosting service like Vercel, Netlify, or GitHub Pages.

1. Build the application:
   ```bash
   npm run build
   ```

2. Deploy the `dist` folder to your hosting service

3. Make sure to set the environment variables in your hosting service's dashboard

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/0e9981b8-0a86-46e2-8927-88fe2015f52c) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
