
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { MessageSquare, Package } from "lucide-react";

interface Invoice {
  id?: string;
  customer: string;
  date: string;
  amount: string;
  status: string;
  device?: string;
  issue?: string;
  gst?: string;
  phone?: string;
  alternatePhone?: string;
  address?: string;
  state?: string;
  city?: string;
  pincode?: string;
  estimatedAmount?: string;
  billableWarranty?: string;
  remarks?: string;
  expectedDelivery?: string;
  inspectionFee?: string;
  usedParts?: { itemId: string; quantity: number; name: string }[];
  expenses?: { description: string; amount: string; category: string }[];
  comments?: { text: string; author: string; timestamp: string; type: 'edit' | 'customer' | 'internal' | 'status_change' }[];
}

interface InvoiceReadOnlyDialogProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: Invoice;
}

export function InvoiceReadOnlyDialog({ isOpen, onClose, invoice }: InvoiceReadOnlyDialogProps) {
  if (!invoice) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>View Final Invoice - {invoice.id}</span>
            <Badge className={getStatusColor(invoice.status)}>
              {invoice.status}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-4">
            {/* Customer Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Customer Name</Label>
                  <Input value={invoice.customer} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Date</Label>
                  <Input value={formatDate(invoice.date)} readOnly className="bg-gray-50" />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>GST (Optional)</Label>
                  <Input value={invoice.gst || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Phone Number</Label>
                  <Input value={invoice.phone || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Alternate Phone</Label>
                  <Input value={invoice.alternatePhone || ""} readOnly className="bg-gray-50" />
                </div>
              </div>

              <div>
                <Label>Address</Label>
                <Textarea value={invoice.address || ""} readOnly className="bg-gray-50" rows={2} />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>State</Label>
                  <Input value={invoice.state || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>City</Label>
                  <Input value={invoice.city || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Pincode</Label>
                  <Input value={invoice.pincode || ""} readOnly className="bg-gray-50" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Device</Label>
                  <Input value={invoice.device || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Issue Description</Label>
                  <Textarea value={invoice.issue || ""} readOnly className="bg-gray-50" rows={1} />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Estimated Amount</Label>
                  <Input value={invoice.estimatedAmount || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Final Amount</Label>
                  <Input value={invoice.amount} readOnly className="bg-gray-50" />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label>Status</Label>
                  <Input value={invoice.status} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Billable/Warranty</Label>
                  <Input value={invoice.billableWarranty || ""} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Inspection Fee</Label>
                  <Input value={invoice.inspectionFee || ""} readOnly className="bg-gray-50" />
                </div>
              </div>

              <div>
                <Label>Expected Delivery Date</Label>
                <Input 
                  value={invoice.expectedDelivery ? formatDate(invoice.expectedDelivery) : ""} 
                  readOnly 
                  className="bg-gray-50" 
                />
              </div>

              {invoice.remarks && (
                <div>
                  <Label>Remarks</Label>
                  <Textarea value={invoice.remarks} readOnly className="bg-gray-50" rows={3} />
                </div>
              )}
            </div>

            {/* Parts Used */}
            {invoice.usedParts && invoice.usedParts.length > 0 && (
              <div className="space-y-3">
                <Label className="flex items-center space-x-2">
                  <Package className="w-4 h-4" />
                  <span>Parts Used</span>
                </Label>
                <div className="space-y-2">
                  {invoice.usedParts.map((part, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded border">
                      <span>{part.name}</span>
                      <span className="font-medium">Qty: {part.quantity}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Additional Expenses */}
            {invoice.expenses && invoice.expenses.length > 0 && (
              <div className="space-y-3">
                <Label>Additional Expenses</Label>
                <div className="space-y-2">
                  {invoice.expenses.map((expense, index) => (
                    <div key={index} className="flex items-center justify-between bg-orange-50 p-2 rounded border border-orange-200">
                      <div>
                        <span className="font-medium">{expense.description}</span>
                        <span className="text-sm text-gray-500 ml-2">({expense.category})</span>
                      </div>
                      <span className="font-medium">{expense.amount}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Comments Section */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <MessageSquare className="w-5 h-5" />
                  <span>Complete Edit & Status History</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-[400px] overflow-y-auto">
                  {invoice.comments && invoice.comments.length > 0 ? (
                    invoice.comments.map((comment, index) => (
                      <div key={index} className="border-l-4 border-orange-200 pl-4 py-2">
                        <div className="flex items-center justify-between mb-1">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-sm">{comment.author}</span>
                            <Badge variant="outline" className="text-xs">
                              {comment.type}
                            </Badge>
                          </div>
                          <span className="text-xs text-gray-500">
                            {new Date(comment.timestamp).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700">{comment.text}</p>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">No comments available</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600">
            Print Invoice
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
