
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { User, Phone, MapPin, FileText, Eye } from "lucide-react";
import { useAppData } from "@/contexts/AppDataContext";
import type { Customer } from "@/hooks/useSupabaseCustomers";

interface CustomerViewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  customer?: Customer;
  onEdit: (customer: Customer) => void;
  onViewInvoice: (invoiceId: string) => void;
}

export function CustomerViewDialog({ 
  isOpen, 
  onClose, 
  customer, 
  onEdit,
  onViewInvoice 
}: CustomerViewDialogProps) {
  const { invoices } = useAppData();

  if (!customer) return null;

  const customerInvoices = invoices.filter(inv => inv.customer === customer.name);
  const totalSpent = customerInvoices
    .filter(inv => inv.status === 'Paid')
    .reduce((sum, inv) => sum + parseFloat(inv.amount.replace(/[₹,]/g, '')), 0);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Customer Details - {customer.name}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Customer Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Personal Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium text-gray-600">Name:</span>
                  <p className="text-gray-900">{customer.name}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Email:</span>
                  <p className="text-gray-900">{customer.email}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Phone:</span>
                  <p className="text-gray-900">{customer.phone}</p>
                </div>
                {customer.alternatePhone && (
                  <div>
                    <span className="font-medium text-gray-600">Alternate Phone:</span>
                    <p className="text-gray-900">{customer.alternatePhone}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center space-x-2">
                  <MapPin className="w-5 h-5" />
                  <span>Address Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="font-medium text-gray-600">Address:</span>
                  <p className="text-gray-900">{customer.address}</p>
                </div>
                {customer.city && (
                  <div>
                    <span className="font-medium text-gray-600">City:</span>
                    <p className="text-gray-900">{customer.city}</p>
                  </div>
                )}
                {customer.state && (
                  <div>
                    <span className="font-medium text-gray-600">State:</span>
                    <p className="text-gray-900">{customer.state}</p>
                  </div>
                )}
                {customer.pincode && (
                  <div>
                    <span className="font-medium text-gray-600">Pincode:</span>
                    <p className="text-gray-900">{customer.pincode}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Customer Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-orange-600">{customerInvoices.length}</div>
                <p className="text-sm text-gray-600">Total Invoices</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">
                  {customerInvoices.filter(inv => inv.status === 'Paid').length}
                </div>
                <p className="text-sm text-gray-600">Paid Invoices</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-blue-600">₹{totalSpent.toLocaleString()}</div>
                <p className="text-sm text-gray-600">Total Spent</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-purple-600">
                  {customerInvoices.filter(inv => inv.status === 'Pending').length}
                </div>
                <p className="text-sm text-gray-600">Pending Invoices</p>
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* Customer Invoices */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="w-5 h-5" />
                <span>Customer Invoices</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {customerInvoices.length > 0 ? (
                <div className="space-y-3">
                  {customerInvoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <span className="font-medium">{invoice.id}</span>
                          <Badge className={
                            invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                            invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }>
                            {invoice.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-gray-600 mt-1">
                          {invoice.date} • {invoice.device} • {invoice.amount}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewInvoice(invoice.id)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No invoices found for this customer</p>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button onClick={() => onEdit(customer)} className="bg-orange-500 hover:bg-orange-600">
              Edit Customer
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
