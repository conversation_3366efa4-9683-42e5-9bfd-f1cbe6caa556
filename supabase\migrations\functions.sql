-- Function to decrease inventory quantity when parts are used in invoices
CREATE OR REPLACE FUNCTION public.decrease_inventory_quantity(item_id UUID, quantity_to_decrease INTEGER)
RETURNS VOID AS $$
BEGIN
  UPDATE public.inventory
  SET quantity = GREATEST(0, quantity - quantity_to_decrease)
  WHERE id = item_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update customer statistics when invoices are created
CREATE OR REPLACE FUNCTION public.update_customer_stats(customer_id UUID, amount DECIMAL, is_service BOOLEAN)
RETURNS VOID AS $$
BEGIN
  UPDATE public.customers
  SET 
    total_spent = total_spent + amount,
    services = CASE WHEN is_service THEN services + 1 ELSE services END,
    purchases = CASE WHEN NOT is_service THEN purchases + 1 ELSE purchases END
  WHERE id = customer_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate trigger for invoices table
CREATE TRIGGER trigger_invoices_updated_at
BEFORE UPDATE ON public.invoices
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();