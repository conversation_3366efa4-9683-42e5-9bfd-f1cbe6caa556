import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';

type InventoryRow = Database['public']['Tables']['inventory_items']['Row'];
type InventoryInsert = Database['public']['Tables']['inventory_items']['Insert'];
type InventoryUpdate = Database['public']['Tables']['inventory_items']['Update'];

export interface InventoryItem {
  id: string;
  name: string;
  description: string | null;
  category: string | null;
  price: string;
  cost: string;
  quantity: number;
  reorder_level: number;
  supplier: string | null;
  created_at: string;
}

// Convert from DB format to app format
function convertFromDb(dbItem: InventoryRow): InventoryItem {
  return {
    id: dbItem.id,
    name: dbItem.name,
    description: dbItem.description,
    category: dbItem.category,
    price: dbItem.price.toString(),
    cost: dbItem.cost.toString(),
    quantity: dbItem.quantity,
    reorder_level: dbItem.reorder_level,
    supplier: dbItem.supplier,
    created_at: dbItem.created_at
  };
}

// Convert to DB insert format
function convertToDbInsert(item: Omit<InventoryItem, 'id' | 'created_at'>): InventoryInsert {
  return {
    name: item.name,
    description: item.description,
    category: item.category,
    price: parseFloat(item.price),
    cost: parseFloat(item.cost),
    quantity: item.quantity,
    reorder_level: item.reorder_level,
    supplier: item.supplier
  };
}

// Convert to DB update format
function convertToDbUpdate(item: Partial<InventoryItem>): InventoryUpdate {
  const update: InventoryUpdate = {};
  
  if (item.name !== undefined) update.name = item.name;
  if (item.description !== undefined) update.description = item.description;
  if (item.category !== undefined) update.category = item.category;
  if (item.price !== undefined) update.price = parseFloat(item.price);
  if (item.cost !== undefined) update.cost = parseFloat(item.cost);
  if (item.quantity !== undefined) update.quantity = item.quantity;
  if (item.reorder_level !== undefined) update.reorder_level = item.reorder_level;
  if (item.supplier !== undefined) update.supplier = item.supplier;
  
  return update;
}

export function useSupabaseInventory() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch inventory
  const {
    data: inventory = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['inventory'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('inventory_items')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching inventory:', error);
        throw error;
      }

      return data.map(convertFromDb);
    }
  });

  // Add inventory item mutation
  const addInventoryMutation = useMutation({
    mutationFn: async (itemData: Omit<InventoryItem, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('inventory_items')
        .insert(convertToDbInsert(itemData))
        .select()
        .single();

      if (error) {
        console.error('Error adding inventory item:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: 'Success',
        description: 'Inventory item added successfully.'
      });
    },
    onError: (error) => {
      console.error('Add inventory error:', error);
      toast({
        title: 'Error',
        description: 'Failed to add inventory item. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update inventory item mutation
  const updateInventoryMutation = useMutation({
    mutationFn: async ({ id, itemData }: { id: string; itemData: Partial<InventoryItem> }) => {
      const { data, error } = await supabase
        .from('inventory_items')
        .update(convertToDbUpdate(itemData))
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating inventory item:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: 'Success',
        description: 'Inventory item updated successfully.'
      });
    },
    onError: (error) => {
      console.error('Update inventory error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update inventory item. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Delete inventory item mutation
  const deleteInventoryMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('inventory_items')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting inventory item:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: 'Success',
        description: 'Inventory item deleted successfully.'
      });
    },
    onError: (error) => {
      console.error('Delete inventory error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete inventory item. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update inventory quantity (for used parts)
  const updateInventoryQuantityMutation = useMutation({
    mutationFn: async ({ id, quantity }: { id: string; quantity: number }) => {
      // First get the current item
      const { data: currentItem, error: fetchError } = await supabase
        .from('inventory_items')
        .select('quantity')
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('Error fetching inventory item:', fetchError);
        throw fetchError;
      }

      // Calculate new quantity
      const newQuantity = Math.max(0, currentItem.quantity - quantity);

      // Update the quantity
      const { data, error } = await supabase
        .from('inventory_items')
        .update({ quantity: newQuantity })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating inventory quantity:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
    },
    onError: (error) => {
      console.error('Update inventory quantity error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update inventory quantity. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Get low stock items
  const getLowStockItems = () => {
    return inventory.filter(item => item.quantity <= item.reorder_level);
  };

  // Get parts used today (placeholder - would need to track usage)
  const getPartsUsedToday = () => {
    // This would typically come from a usage tracking table
    // For now, return empty array as placeholder
    return [];
  };

  return {
    inventory,
    isLoading,
    error,
    addInventory: (itemData: Omit<InventoryItem, 'id' | 'created_at'>) =>
      addInventoryMutation.mutateAsync(itemData),
    updateInventory: (id: string, itemData: Partial<InventoryItem>) =>
      updateInventoryMutation.mutateAsync({ id, itemData }),
    deleteInventory: (id: string) =>
      deleteInventoryMutation.mutateAsync(id),
    updateInventoryQuantity: (id: string, quantity: number) =>
      updateInventoryQuantityMutation.mutateAsync({ id, quantity }),
    getLowStockItems,
    getPartsUsedToday
  };
}

