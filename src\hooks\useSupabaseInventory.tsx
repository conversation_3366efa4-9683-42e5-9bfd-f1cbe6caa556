import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';

type InventoryRow = Database['public']['Tables']['inventory_items']['Row'];
type InventoryInsert = Database['public']['Tables']['inventory_items']['Insert'];
type InventoryUpdate = Database['public']['Tables']['inventory_items']['Update'];

export interface InventoryItem {
  id: string;
  name: string;
  category: string | null;
  current_stock: number;
  minimum_stock: number;
  unit_price: string;
  supplier: string | null;
  location: string | null;
  total_sales: number;
  sold_today: number;
  created_at: string;
  updated_at: string;
}

// Convert from DB format to app format
function convertFromDb(dbItem: any): InventoryItem {
  return {
    id: dbItem.id,
    name: dbItem.name,
    category: dbItem.category,
    current_stock: dbItem.current_stock,
    minimum_stock: dbItem.minimum_stock,
    unit_price: dbItem.unit_price.toString(),
    supplier: dbItem.supplier,
    location: dbItem.location,
    total_sales: dbItem.total_sales,
    sold_today: dbItem.sold_today,
    created_at: dbItem.created_at,
    updated_at: dbItem.updated_at
  };
}

// Convert to DB insert format
function convertToDbInsert(item: Omit<InventoryItem, 'id' | 'created_at' | 'updated_at'>): any {
  return {
    name: item.name,
    category: item.category,
    current_stock: item.current_stock,
    minimum_stock: item.minimum_stock,
    unit_price: parseFloat(item.unit_price),
    supplier: item.supplier,
    location: item.location,
    total_sales: item.total_sales,
    sold_today: item.sold_today
  };
}

// Convert to DB update format
function convertToDbUpdate(item: Partial<InventoryItem>): any {
  const update: any = {};

  if (item.name !== undefined) update.name = item.name;
  if (item.category !== undefined) update.category = item.category;
  if (item.current_stock !== undefined) update.current_stock = item.current_stock;
  if (item.minimum_stock !== undefined) update.minimum_stock = item.minimum_stock;
  if (item.unit_price !== undefined) update.unit_price = parseFloat(item.unit_price);
  if (item.supplier !== undefined) update.supplier = item.supplier;
  if (item.location !== undefined) update.location = item.location;
  if (item.total_sales !== undefined) update.total_sales = item.total_sales;
  if (item.sold_today !== undefined) update.sold_today = item.sold_today;

  return update;
}

export function useSupabaseInventory() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch inventory
  const {
    data: inventory = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['inventory'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('inventory_items')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching inventory:', error);
        throw error;
      }

      return data.map(convertFromDb);
    }
  });

  // Add inventory item mutation
  const addInventoryMutation = useMutation({
    mutationFn: async (itemData: Omit<InventoryItem, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('inventory_items')
        .insert(convertToDbInsert(itemData))
        .select()
        .single();

      if (error) {
        console.error('Error adding inventory item:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: 'Success',
        description: 'Inventory item added successfully.'
      });
    },
    onError: (error) => {
      console.error('Add inventory error:', error);
      toast({
        title: 'Error',
        description: 'Failed to add inventory item. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update inventory item mutation
  const updateInventoryMutation = useMutation({
    mutationFn: async ({ id, itemData }: { id: string; itemData: Partial<InventoryItem> }) => {
      const { data, error } = await supabase
        .from('inventory_items')
        .update(convertToDbUpdate(itemData))
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating inventory item:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: 'Success',
        description: 'Inventory item updated successfully.'
      });
    },
    onError: (error) => {
      console.error('Update inventory error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update inventory item. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Delete inventory item mutation
  const deleteInventoryMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('inventory_items')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting inventory item:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: 'Success',
        description: 'Inventory item deleted successfully.'
      });
    },
    onError: (error) => {
      console.error('Delete inventory error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete inventory item. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update inventory quantity (for used parts)
  const updateInventoryQuantityMutation = useMutation({
    mutationFn: async ({ id, quantity }: { id: string; quantity: number }) => {
      // First get the current item
      const { data: currentItem, error: fetchError } = await supabase
        .from('inventory_items')
        .select('quantity')
        .eq('id', id)
        .single();

      if (fetchError) {
        console.error('Error fetching inventory item:', fetchError);
        throw fetchError;
      }

      // Calculate new quantity
      const newQuantity = Math.max(0, currentItem.quantity - quantity);

      // Update the quantity
      const { data, error } = await supabase
        .from('inventory_items')
        .update({ quantity: newQuantity })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating inventory quantity:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
    },
    onError: (error) => {
      console.error('Update inventory quantity error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update inventory quantity. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Get low stock items
  const getLowStockItems = () => {
    return inventory.filter(item => item.current_stock <= item.minimum_stock);
  };

  // Get parts used today (placeholder - would need to track usage)
  const getPartsUsedToday = () => {
    // This would typically come from a usage tracking table
    // For now, return empty array as placeholder
    return [];
  };

  return {
    inventory,
    isLoading,
    error,
    addInventory: (itemData: Omit<InventoryItem, 'id' | 'created_at'>) =>
      addInventoryMutation.mutateAsync(itemData),
    updateInventory: (id: string, itemData: Partial<InventoryItem>) =>
      updateInventoryMutation.mutateAsync({ id, itemData }),
    deleteInventory: (id: string) =>
      deleteInventoryMutation.mutateAsync(id),
    updateInventoryQuantity: (id: string, quantity: number) =>
      updateInventoryQuantityMutation.mutateAsync({ id, quantity }),
    getLowStockItems,
    getPartsUsedToday
  };
}

