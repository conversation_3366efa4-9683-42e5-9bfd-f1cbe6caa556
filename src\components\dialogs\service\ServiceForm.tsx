
import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface Service {
  id?: string;
  customer: string;
  phone: string;
  device: string;
  issue: string;
  status: string;
  technician: string;
  cost: string;
  comments?: { text: string; author: string; timestamp: string; type: 'edit' | 'customer' | 'internal' | 'status_change' }[];
}

interface ServiceFormProps {
  formData: Service;
  setFormData: (data: Service) => void;
  originalStatus: string;
  service?: Service;
}

const statusOptions = [
  "Pending",
  "In Progress", 
  "Approval",
  "Ready/Return",
  "Completed"
];

export function ServiceForm({ formData, setFormData, originalStatus, service }: ServiceFormProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="customer">Customer Name</Label>
          <Input
            id="customer"
            value={formData.customer}
            onChange={(e) => setFormData({...formData, customer: e.target.value})}
            required
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => setFormData({...formData, phone: e.target.value})}
            required
          />
        </div>
      </div>
      
      <div>
        <Label htmlFor="device">Device</Label>
        <Input
          id="device"
          value={formData.device}
          onChange={(e) => setFormData({...formData, device: e.target.value})}
          required
        />
      </div>
      
      <div>
        <Label htmlFor="issue">Issue Description</Label>
        <Textarea
          id="issue"
          value={formData.issue}
          onChange={(e) => setFormData({...formData, issue: e.target.value})}
          rows={3}
          required
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              {statusOptions.map(status => (
                <SelectItem key={status} value={status}>{status}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          {service && originalStatus !== formData.status && (
            <p className="text-xs text-orange-600 mt-1">
              Status will change from {originalStatus} to {formData.status}
            </p>
          )}
        </div>
        <div>
          <Label htmlFor="cost">Cost</Label>
          <Input
            id="cost"
            placeholder="₹0"
            value={formData.cost}
            onChange={(e) => setFormData({...formData, cost: e.target.value})}
            required
          />
        </div>
      </div>
      
      <div>
        <Label htmlFor="technician">Technician</Label>
        <Select value={formData.technician} onValueChange={(value) => setFormData({...formData, technician: value})}>
          <SelectTrigger>
            <SelectValue placeholder="Select technician" />
          </SelectTrigger>
          <SelectContent className="bg-white z-50">
            <SelectItem value="Not assigned">Not assigned</SelectItem>
            <SelectItem value="Mark Wilson">Mark Wilson</SelectItem>
            <SelectItem value="Jane Smith">Jane Smith</SelectItem>
            <SelectItem value="Bob Johnson">Bob Johnson</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
