
import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useSupabaseAppData } from "@/contexts/SupabaseAppDataContext";
import { AlertTriangle } from "lucide-react";
import { InvoiceForm } from "./invoice/InvoiceForm";
import { PartsManager } from "./invoice/PartsManager";
import { ExpensesManager } from "./invoice/ExpensesManager";
import { CommentsSection } from "./invoice/CommentsSection";
import type { Invoice, Service, UsedPart, ExpenseItem } from "../../types";

interface InvoiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  invoice?: Invoice;
  onSave: (invoice: Invoice) => void;
}

export function InvoiceDialog({ isOpen, onClose, invoice, onSave }: InvoiceDialogProps) {
  const { inventory, addExpense, getLowStockItems, services, updateInventory } = useSupabaseAppData();
  
  const [formData, setFormData] = useState<Invoice>({
    customer: "",
    date: new Date().toISOString().split('T')[0],
    amount: "",
    status: "Pending",
    device: "",
    deviceType: "",
    customDeviceName: "",
    issue: "",
    gst: "",
    phone: "",
    alternatePhone: "",
    address: "",
    state: "",
    city: "",
    pincode: "",
    estimatedAmount: "",
    billableWarranty: "",
    showRemarks: false,
    remarks: "",
    expectedDelivery: "",
    inspectionFee: "500",
    usedParts: [],
    expenses: [],
    comments: []
  });

  const [originalData, setOriginalData] = useState<Invoice | null>(null);
  const [newComment, setNewComment] = useState("");
  const [commentType, setCommentType] = useState<'edit' | 'customer' | 'internal' | 'status_change'>('internal');

  const lowStockItems = getLowStockItems();
  const associatedService = invoice?.id ? services.find(s => s.id === invoice.id) : null;

  // Reset form data when dialog opens/closes or invoice changes
  useEffect(() => {
    if (isOpen) {
      if (invoice) {
        console.log('Setting form data from invoice:', invoice);
        setFormData({
          customer: invoice.customer || "",
          date: invoice.date || new Date().toISOString().split('T')[0],
          amount: invoice.amount || "",
          status: invoice.status || "Pending",
          device: invoice.device || "",
          deviceType: invoice.deviceType || "",
          customDeviceName: invoice.customDeviceName || "",
          issue: invoice.issue || "",
          gst: invoice.gst || "",
          phone: invoice.phone || "",
          alternatePhone: invoice.alternatePhone || "",
          address: invoice.address || "",
          state: invoice.state || "",
          city: invoice.city || "",
          pincode: invoice.pincode || "",
          estimatedAmount: invoice.estimatedAmount || "",
          billableWarranty: invoice.billableWarranty || "",
          showRemarks: invoice.showRemarks || false,
          remarks: invoice.remarks || "",
          expectedDelivery: invoice.expectedDelivery || "",
          inspectionFee: invoice.inspectionFee || "500",
          usedParts: invoice.usedParts || [],
          expenses: invoice.expenses || [],
          comments: invoice.comments || [],
          ...invoice
        });
        setOriginalData(JSON.parse(JSON.stringify(invoice)));
      } else {
        // Calculate expected delivery for new invoice
        const billDate = new Date().toISOString().split('T')[0];
        const deliveryDate = new Date();
        deliveryDate.setDate(deliveryDate.getDate() + 3);
        
        setFormData({
          customer: "",
          date: billDate,
          amount: "",
          status: "Pending",
          device: "",
          deviceType: "",
          customDeviceName: "",
          issue: "",
          gst: "",
          phone: "",
          alternatePhone: "",
          address: "",
          state: "",
          city: "",
          pincode: "",
          estimatedAmount: "",
          billableWarranty: "",
          showRemarks: false,
          remarks: "",
          expectedDelivery: deliveryDate.toISOString().split('T')[0],
          inspectionFee: "500",
          usedParts: [],
          expenses: [],
          comments: []
        });
        setOriginalData(null);
      }
      // Reset comment form
      setNewComment("");
      setCommentType('internal');
    }
  }, [isOpen, invoice]);

  const handleUpdateParts = (parts: UsedPart[]) => {
    setFormData(prev => ({ ...prev, usedParts: parts }));
  };

  const handleUpdateExpenses = (expenses: ExpenseItem[]) => {
    setFormData(prev => ({ ...prev, expenses }));
  };

  const getAllComments = () => {
    const invoiceComments = formData.comments || [];
    const serviceComments = associatedService?.comments || [];
    
    const allComments = [...invoiceComments, ...serviceComments].sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    );
    
    return allComments;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Track what changes were made
    const changes: string[] = [];
    if (originalData) {
      if (originalData.amount !== formData.amount) {
        changes.push(`Amount changed from ${originalData.amount} to ${formData.amount}`);
      }
      if (originalData.status !== formData.status) {
        changes.push(`Status changed from ${originalData.status} to ${formData.status}`);
      }
      if (originalData.device !== formData.device) {
        changes.push(`Device updated to ${formData.device}`);
      }
      if (originalData.issue !== formData.issue) {
        changes.push(`Issue description updated`);
      }
    }

    // Add automatic edit comment if changes were made
    const updatedComments = [...(formData.comments || [])];
    if (changes.length > 0) {
      updatedComments.push({
        text: `Invoice updated: ${changes.join(', ')}`,
        author: 'System',
        timestamp: new Date().toISOString(),
        type: 'edit'
      });
    }

    // Add "Invoice created" comment for new invoices
    if (!originalData) {
      updatedComments.push({
        text: 'Invoice created',
        author: 'System',
        timestamp: new Date().toISOString(),
        type: 'edit'
      });
    }

    // Add manual comment if provided
    if (newComment.trim()) {
      updatedComments.push({
        text: newComment.trim(),
        author: 'Current User',
        timestamp: new Date().toISOString(),
        type: commentType
      });
    }

    const finalData = {
      ...formData,
      comments: updatedComments
    };

    // Update inventory for used parts (only for non-custom parts)
    formData.usedParts?.forEach(part => {
      if (!part.itemId.startsWith('custom-')) {
        updateInventory(part.itemId, part.quantity);
      }
    });

    // Add ALL expenses to the expense tracker
    formData.expenses?.forEach(expense => {
      addExpense({
        date: formData.date,
        description: expense.description,
        category: expense.category,
        amount: expense.amount,
        vendor: "Internal",
        receipt: "Available",
        invoiceId: invoice?.id || `INV-${Date.now()}`
      });
    });

    onSave(finalData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[1200px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{invoice ? 'Edit Final Invoice' : 'Create Final Invoice'}</DialogTitle>
        </DialogHeader>

        {lowStockItems.length > 0 && (
          <div className="bg-red-50 p-3 rounded-lg mb-4">
            <div className="flex items-center space-x-2 text-red-800">
              <AlertTriangle className="w-4 h-4" />
              <span className="font-medium">Low Stock Alert</span>
            </div>
            <p className="text-sm text-red-600 mt-1">
              {lowStockItems.length} items are low in stock: {lowStockItems.map(item => item.name).join(', ')}
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-4">
              <InvoiceForm formData={formData} setFormData={setFormData} />
              
              <PartsManager
                inventory={inventory}
                usedParts={formData.usedParts || []}
                onUpdateParts={handleUpdateParts}
              />

              <ExpensesManager
                expenses={formData.expenses || []}
                onUpdateExpenses={handleUpdateExpenses}
              />
            </div>

            <div className="space-y-4">
              <CommentsSection
                allComments={getAllComments()}
                newComment={newComment}
                setNewComment={setNewComment}
                commentType={commentType}
                setCommentType={setCommentType}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
              {invoice ? 'Update' : 'Create'} Final Invoice
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
