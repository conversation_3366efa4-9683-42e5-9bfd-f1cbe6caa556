
import { Card, CardContent } from "@/components/ui/card";

interface CustomerStatisticsProps {
  customerInvoices: any[];
  totalSpent: number;
}

export function CustomerStatistics({ customerInvoices, totalSpent }: CustomerStatisticsProps) {
  return (
    <div className="grid grid-cols-4 gap-4">
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{customerInvoices.length}</div>
          <p className="text-sm text-gray-600">Total Invoices</p>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {customerInvoices.filter(inv => inv.status === 'Paid').length}
          </div>
          <p className="text-sm text-gray-600">Paid Invoices</p>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">₹{totalSpent.toLocaleString()}</div>
          <p className="text-sm text-gray-600">Total Spent</p>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">
            {customerInvoices.filter(inv => inv.status === 'Pending').length}
          </div>
          <p className="text-sm text-gray-600">Pending Invoices</p>
        </CardContent>
      </Card>
    </div>
  );
}
