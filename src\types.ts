
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  minimumStock: number;
  unitPrice: string;
  supplier: string;
  location: string;
  totalSales?: number;
  soldToday?: number;
}

export interface Comment {
  text: string;
  author: string;
  timestamp: string;
  type: 'edit' | 'customer' | 'internal' | 'status_change';
}

export interface UsedPart {
  itemId: string;
  quantity: number;
  name: string;
  manufacturer?: string;
  model?: string;
  serialNo?: string;
}

export interface ExpenseItem {
  description: string;
  amount: string;
  category: string;
}

export interface Invoice {
  id?: string;
  customer: string;
  date: string;
  amount: string;
  status: string;
  items?: string[];
  device?: string;
  deviceType?: string;
  customDeviceName?: string;
  issue?: string;
  gst?: string;
  phone?: string;
  alternatePhone?: string;
  address?: string;
  state?: string;
  city?: string;
  pincode?: string;
  estimatedAmount?: string;
  billableWarranty?: string;
  showRemarks?: boolean;
  remarks?: string;
  expectedDelivery?: string;
  inspectionFee?: string;
  usedParts?: UsedPart[];
  expenses?: ExpenseItem[];
  comments?: Comment[];
}

export interface Service {
  id: string;
  customer: string;
  phone: string;
  device: string;
  issue: string;
  status: string;
  technician: string;
  cost: string;
  invoiceId?: string;
  comments?: Comment[];
}

// Update the Customer interface to match the Supabase hook
export interface Customer {
  id?: string; // Make id optional to match useSupabaseCustomers
  name: string;
  email?: string;
  phone: string;
  alternatePhone?: string;
  address: string;
  state?: string;
  city?: string;
  pincode?: string;
  purchases?: number;
  services?: number;
  totalSpent?: string;
  warranty?: string;
}

export interface Expense {
  id: string;
  date: string;
  description: string;
  category: string;
  amount: string;
  vendor: string;
  receipt: string;
  invoiceId?: string;
}
