import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { Database } from '@/integrations/supabase/types';

type ExpenseRow = Database['public']['Tables']['expenses']['Row'];
type ExpenseInsert = Database['public']['Tables']['expenses']['Insert'];
type ExpenseUpdate = Database['public']['Tables']['expenses']['Update'];

export interface Expense {
  id: string;
  description: string;
  amount: string;
  category: string;
  date: string;
  paymentMethod: string;
  notes?: string;
  created_at: string;
}

// Convert from DB format to app format
function convertFromDb(dbExpense: ExpenseRow): Expense {
  return {
    id: dbExpense.id,
    description: dbExpense.description,
    amount: dbExpense.amount.toString(),
    category: dbExpense.category,
    date: dbExpense.date,
    paymentMethod: dbExpense.payment_method,
    notes: dbExpense.notes || '',
    created_at: dbExpense.created_at
  };
}

// Convert to DB insert format
function convertToDbInsert(expense: Omit<Expense, 'id' | 'created_at'>): ExpenseInsert {
  return {
    description: expense.description,
    amount: parseFloat(expense.amount.replace(/[₹,]/g, '')),
    category: expense.category,
    date: expense.date,
    payment_method: expense.paymentMethod,
    notes: expense.notes
  };
}

// Convert to DB update format
function convertToDbUpdate(expense: Partial<Expense>): ExpenseUpdate {
  const update: ExpenseUpdate = {};
  
  if (expense.description) update.description = expense.description;
  if (expense.amount) update.amount = parseFloat(expense.amount.replace(/[₹,]/g, ''));
  if (expense.category) update.category = expense.category;
  if (expense.date) update.date = expense.date;
  if (expense.paymentMethod) update.payment_method = expense.paymentMethod;
  if (expense.notes !== undefined) update.notes = expense.notes;
  
  return update;
}

export function useSupabaseExpenses() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch expenses
  const {
    data: expenses = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['expenses'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('expenses')
        .select('*')
        .order('date', { ascending: false });

      if (error) {
        console.error('Error fetching expenses:', error);
        throw error;
      }

      return data.map(convertFromDb);
    }
  });

  // Add expense mutation
  const addExpenseMutation = useMutation({
    mutationFn: async (expenseData: Omit<Expense, 'id' | 'created_at'>) => {
      const { data, error } = await supabase
        .from('expenses')
        .insert(convertToDbInsert(expenseData))
        .select()
        .single();

      if (error) {
        console.error('Error adding expense:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      toast({
        title: 'Success',
        description: 'Expense added successfully.'
      });
    },
    onError: (error) => {
      console.error('Add expense error:', error);
      toast({
        title: 'Error',
        description: 'Failed to add expense. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Update expense mutation
  const updateExpenseMutation = useMutation({
    mutationFn: async ({ id, expenseData }: { id: string; expenseData: Partial<Expense> }) => {
      const { data, error } = await supabase
        .from('expenses')
        .update(convertToDbUpdate(expenseData))
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating expense:', error);
        throw error;
      }

      return convertFromDb(data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      toast({
        title: 'Success',
        description: 'Expense updated successfully.'
      });
    },
    onError: (error) => {
      console.error('Update expense error:', error);
      toast({
        title: 'Error',
        description: 'Failed to update expense. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Delete expense mutation
  const deleteExpenseMutation = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('expenses')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting expense:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      toast({
        title: 'Success',
        description: 'Expense deleted successfully.'
      });
    },
    onError: (error) => {
      console.error('Delete expense error:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete expense. Please try again.',
        variant: 'destructive'
      });
    }
  });

  // Get today's expenses
  const getTodaysExpenses = () => {
    const today = new Date().toISOString().split('T')[0];
    return expenses.filter(expense => expense.date === today);
  };

  return {
    expenses,
    isLoading,
    error,
    addExpense: (expenseData: Omit<Expense, 'id' | 'created_at'>) => 
      addExpenseMutation.mutateAsync(expenseData),
    updateExpense: (id: string, expenseData: Partial<Expense>) => 
      updateExpenseMutation.mutateAsync({ id, expenseData }),
    deleteExpense: (id: string) => deleteExpenseMutation.mutateAsync(id),
    getTodaysExpenses
  };
}