
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for better data integrity
CREATE TYPE invoice_status AS ENUM ('Draft', 'Pending', 'Paid', 'Overdue', 'Cancelled');
CREATE TYPE service_status AS ENUM ('Pending', 'In Progress', 'Completed', 'Cancelled', 'On Hold');
CREATE TYPE expense_category AS ENUM ('Office', 'Marketing', 'Equipment', 'Utilities', 'Travel', 'Parts', 'Labor', 'Shipping', 'Other');
CREATE TYPE comment_type AS ENUM ('edit', 'customer', 'internal', 'status_change');
CREATE TYPE receipt_status AS ENUM ('Available', 'Pending', 'Missing');

-- Create customers table
CREATE TABLE public.customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT,
    phone TEXT NOT NULL,
    alternate_phone TEXT,
    address TEXT NOT NULL,
    state TEXT,
    city TEXT,
    pincode TEXT,
    purchases INTEGER DEFAULT 0,
    services INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    warranty TEXT DEFAULT 'N/A',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create inventory_items table
CREATE TABLE public.inventory_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    current_stock INTEGER NOT NULL DEFAULT 0,
    minimum_stock INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    supplier TEXT NOT NULL,
    location TEXT NOT NULL,
    total_sales INTEGER DEFAULT 0,
    sold_today INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create services table
CREATE TABLE public.services (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    phone TEXT NOT NULL,
    device TEXT NOT NULL,
    issue TEXT NOT NULL,
    status service_status DEFAULT 'Pending',
    technician TEXT DEFAULT 'Not assigned',
    cost DECIMAL(10,2) DEFAULT 0,
    invoice_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create invoices table
CREATE TABLE public.invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES public.customers(id) ON DELETE SET NULL,
    customer_name TEXT NOT NULL,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    amount DECIMAL(10,2) NOT NULL,
    status invoice_status DEFAULT 'Pending',
    device TEXT,
    device_type TEXT,
    custom_device_name TEXT,
    issue TEXT,
    gst DECIMAL(10,2) DEFAULT 0,
    phone TEXT,
    alternate_phone TEXT,
    address TEXT,
    state TEXT,
    city TEXT,
    pincode TEXT,
    estimated_amount DECIMAL(10,2),
    billable_warranty TEXT,
    show_remarks BOOLEAN DEFAULT FALSE,
    remarks TEXT,
    expected_delivery DATE,
    inspection_fee DECIMAL(10,2) DEFAULT 500,
    service_id UUID REFERENCES public.services(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create expenses table
CREATE TABLE public.expenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    description TEXT NOT NULL,
    category expense_category NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    vendor TEXT NOT NULL,
    receipt receipt_status DEFAULT 'Pending',
    invoice_id UUID REFERENCES public.invoices(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create used_parts table (for invoice parts tracking)
CREATE TABLE public.used_parts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
    item_id UUID REFERENCES public.inventory_items(id) ON DELETE SET NULL,
    item_name TEXT NOT NULL,
    quantity INTEGER NOT NULL,
    manufacturer TEXT,
    model TEXT,
    serial_no TEXT,
    is_custom BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create comments table (for both invoices and services)
CREATE TABLE public.comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES public.invoices(id) ON DELETE CASCADE,
    service_id UUID REFERENCES public.services(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    author TEXT NOT NULL,
    type comment_type NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint for services.invoice_id after invoices table is created
ALTER TABLE public.services ADD CONSTRAINT fk_services_invoice_id 
    FOREIGN KEY (invoice_id) REFERENCES public.invoices(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX idx_customers_phone ON public.customers(phone);
CREATE INDEX idx_customers_email ON public.customers(email);
CREATE INDEX idx_invoices_customer_id ON public.invoices(customer_id);
CREATE INDEX idx_invoices_status ON public.invoices(status);
CREATE INDEX idx_invoices_date ON public.invoices(date);
CREATE INDEX idx_services_customer_id ON public.services(customer_id);
CREATE INDEX idx_services_status ON public.services(status);
CREATE INDEX idx_expenses_category ON public.expenses(category);
CREATE INDEX idx_expenses_date ON public.expenses(date);
CREATE INDEX idx_used_parts_invoice_id ON public.used_parts(invoice_id);
CREATE INDEX idx_used_parts_item_id ON public.used_parts(item_id);
CREATE INDEX idx_comments_invoice_id ON public.comments(invoice_id);
CREATE INDEX idx_comments_service_id ON public.comments(service_id);
CREATE INDEX idx_inventory_items_category ON public.inventory_items(category);

-- Enable Row Level Security on all tables
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.inventory_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.used_parts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (allowing all operations for authenticated users for now)
-- In a production environment, you would implement proper user roles and permissions

-- Customers policies
CREATE POLICY "Allow all operations on customers" ON public.customers FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Inventory policies
CREATE POLICY "Allow all operations on inventory_items" ON public.inventory_items FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Services policies
CREATE POLICY "Allow all operations on services" ON public.services FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Invoices policies
CREATE POLICY "Allow all operations on invoices" ON public.invoices FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Expenses policies
CREATE POLICY "Allow all operations on expenses" ON public.expenses FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Used parts policies
CREATE POLICY "Allow all operations on used_parts" ON public.used_parts FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Comments policies
CREATE POLICY "Allow all operations on comments" ON public.comments FOR ALL TO authenticated USING (true) WITH CHECK (true);

-- Create functions for business logic

-- Function to update customer statistics
CREATE OR REPLACE FUNCTION update_customer_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- Update customer purchase count and total spent from invoices
        UPDATE public.customers 
        SET 
            purchases = (
                SELECT COUNT(*) 
                FROM public.invoices 
                WHERE customer_id = NEW.customer_id AND status = 'Paid'
            ),
            total_spent = (
                SELECT COALESCE(SUM(amount), 0) 
                FROM public.invoices 
                WHERE customer_id = NEW.customer_id AND status = 'Paid'
            ),
            services = (
                SELECT COUNT(*) 
                FROM public.services 
                WHERE customer_id = NEW.customer_id
            ),
            updated_at = NOW()
        WHERE id = NEW.customer_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Update customer stats after deletion
        UPDATE public.customers 
        SET 
            purchases = (
                SELECT COUNT(*) 
                FROM public.invoices 
                WHERE customer_id = OLD.customer_id AND status = 'Paid'
            ),
            total_spent = (
                SELECT COALESCE(SUM(amount), 0) 
                FROM public.invoices 
                WHERE customer_id = OLD.customer_id AND status = 'Paid'
            ),
            services = (
                SELECT COUNT(*) 
                FROM public.services 
                WHERE customer_id = OLD.customer_id
            ),
            updated_at = NOW()
        WHERE id = OLD.customer_id;
        
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update inventory when parts are used
CREATE OR REPLACE FUNCTION update_inventory_on_part_usage()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Decrease stock when part is used (only for non-custom parts)
        IF NEW.item_id IS NOT NULL AND NOT NEW.is_custom THEN
            UPDATE public.inventory_items 
            SET 
                current_stock = current_stock - NEW.quantity,
                total_sales = total_sales + NEW.quantity,
                updated_at = NOW()
            WHERE id = NEW.item_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Handle quantity changes
        IF OLD.item_id IS NOT NULL AND NOT OLD.is_custom THEN
            -- Restore old quantity
            UPDATE public.inventory_items 
            SET 
                current_stock = current_stock + OLD.quantity,
                total_sales = total_sales - OLD.quantity,
                updated_at = NOW()
            WHERE id = OLD.item_id;
        END IF;
        
        IF NEW.item_id IS NOT NULL AND NOT NEW.is_custom THEN
            -- Apply new quantity
            UPDATE public.inventory_items 
            SET 
                current_stock = current_stock - NEW.quantity,
                total_sales = total_sales + NEW.quantity,
                updated_at = NOW()
            WHERE id = NEW.item_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Restore inventory when part usage is deleted
        IF OLD.item_id IS NOT NULL AND NOT OLD.is_custom THEN
            UPDATE public.inventory_items 
            SET 
                current_stock = current_stock + OLD.quantity,
                total_sales = total_sales - OLD.quantity,
                updated_at = NOW()
            WHERE id = OLD.item_id;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers

-- Customer statistics triggers
CREATE TRIGGER trigger_update_customer_stats_on_invoice
    AFTER INSERT OR UPDATE OR DELETE ON public.invoices
    FOR EACH ROW EXECUTE FUNCTION update_customer_stats();

CREATE TRIGGER trigger_update_customer_stats_on_service
    AFTER INSERT OR UPDATE OR DELETE ON public.services
    FOR EACH ROW EXECUTE FUNCTION update_customer_stats();

-- Inventory update trigger
CREATE TRIGGER trigger_update_inventory_on_part_usage
    AFTER INSERT OR UPDATE OR DELETE ON public.used_parts
    FOR EACH ROW EXECUTE FUNCTION update_inventory_on_part_usage();

-- Updated_at triggers
CREATE TRIGGER trigger_customers_updated_at
    BEFORE UPDATE ON public.customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_inventory_items_updated_at
    BEFORE UPDATE ON public.inventory_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_services_updated_at
    BEFORE UPDATE ON public.services
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_invoices_updated_at
    BEFORE UPDATE ON public.invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_expenses_updated_at
    BEFORE UPDATE ON public.expenses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable realtime for all tables
ALTER TABLE public.customers REPLICA IDENTITY FULL;
ALTER TABLE public.inventory_items REPLICA IDENTITY FULL;
ALTER TABLE public.services REPLICA IDENTITY FULL;
ALTER TABLE public.invoices REPLICA IDENTITY FULL;
ALTER TABLE public.expenses REPLICA IDENTITY FULL;
ALTER TABLE public.used_parts REPLICA IDENTITY FULL;
ALTER TABLE public.comments REPLICA IDENTITY FULL;

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.customers;
ALTER PUBLICATION supabase_realtime ADD TABLE public.inventory_items;
ALTER PUBLICATION supabase_realtime ADD TABLE public.services;
ALTER PUBLICATION supabase_realtime ADD TABLE public.invoices;
ALTER PUBLICATION supabase_realtime ADD TABLE public.expenses;
ALTER PUBLICATION supabase_realtime ADD TABLE public.used_parts;
ALTER PUBLICATION supabase_realtime ADD TABLE public.comments;
