
import { useState } from 'react';

interface Invoice {
  id: string;
  customer: string;
  date: string;
  amount: string;
  status: string;
  items?: string[];
  usedParts?: { itemId: string; quantity: number; name: string }[];
  device?: string;
  issue?: string;
  gst?: string;
  phone?: string;
  alternatePhone?: string;
  address?: string;
  state?: string;
  city?: string;
  pincode?: string;
  estimatedAmount?: string;
  billableWarranty?: string;
  showRemarks?: boolean;
  remarks?: string;
  expectedDelivery?: string;
  inspectionFee?: string;
  expenses?: { description: string; amount: string; category: string }[];
  comments?: { text: string; author: string; timestamp: string; type: 'edit' | 'customer' | 'internal' | 'status_change' }[];
}

export function useInvoices() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);

  const addInvoice = (invoiceData: Omit<Invoice, 'id'>): Invoice => {
    console.log('Adding invoice:', invoiceData);
    const newInvoice: Invoice = {
      ...invoiceData,
      id: `INV-${String(invoices.length + 1).padStart(3, '0')}`,
      comments: invoiceData.comments || []
    };
    
    setInvoices(prev => {
      const updated = [...prev, newInvoice];
      console.log('Invoices updated:', updated);
      return updated;
    });
    
    return newInvoice;
  };

  const updateInvoice = (id: string, invoiceData: Partial<Invoice>) => {
    console.log('Updating invoice:', id, invoiceData);
    setInvoices(prev => {
      const updated = prev.map(invoice =>
        invoice.id === id ? { ...invoice, ...invoiceData } : invoice
      );
      console.log('Invoices updated after edit:', updated);
      return updated;
    });
  };

  const deleteInvoice = (id: string) => {
    setInvoices(prev => prev.filter(invoice => invoice.id !== id));
  };

  return {
    invoices,
    addInvoice,
    updateInvoice,
    deleteInvoice,
    setInvoices
  };
}
