
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ServiceForm } from "./service/ServiceForm";

interface Service {
  id?: string;
  customer: string;
  phone: string;
  device: string;
  issue: string;
  status: string;
  technician: string;
  cost: string;
  comments?: { text: string; author: string; timestamp: string; type: 'edit' | 'customer' | 'internal' | 'status_change' }[];
}

interface ServiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  service?: Service;
  onSave: (service: Service) => void;
}

export function ServiceDialog({ isOpen, onClose, service, onSave }: ServiceDialogProps) {
  const [formData, setFormData] = useState<Service>({
    customer: "",
    phone: "",
    device: "",
    issue: "",
    status: "Pending",
    technician: "Not assigned",
    cost: "",
    comments: []
  });

  const [originalStatus, setOriginalStatus] = useState<string>("");

  useEffect(() => {
    if (isOpen) {
      if (service) {
        setFormData({
          customer: service.customer || "",
          phone: service.phone || "",
          device: service.device || "",
          issue: service.issue || "",
          status: service.status || "Pending",
          technician: service.technician || "Not assigned",
          cost: service.cost || "",
          comments: service.comments || [],
          ...service
        });
        setOriginalStatus(service.status || "Pending");
      } else {
        setFormData({
          customer: "",
          phone: "",
          device: "",
          issue: "",
          status: "Pending",
          technician: "Not assigned",
          cost: "",
          comments: []
        });
        setOriginalStatus("Pending");
      }
    }
  }, [isOpen, service]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Track status change if service is being updated and status changed
    const updatedComments = [...(formData.comments || [])];
    if (service && originalStatus !== formData.status) {
      updatedComments.push({
        text: `Service status changed from ${originalStatus} to ${formData.status}`,
        author: 'Current User',
        timestamp: new Date().toISOString(),
        type: 'status_change'
      });
    }
    
    const finalData = {
      ...formData,
      comments: updatedComments
    };
    
    onSave(finalData);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{service ? 'Edit Service Request' : 'Create New Service Request'}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <ServiceForm 
            formData={formData}
            setFormData={setFormData}
            originalStatus={originalStatus}
            service={service}
          />
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" className="bg-orange-500 hover:bg-orange-600">
              {service ? 'Update' : 'Create'} Service
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
