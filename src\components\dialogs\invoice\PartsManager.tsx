
import { useState } from "react";
import { PartsSection } from "./PartsSection";
import type { InventoryItem, UsedPart } from "../../../types";

interface PartsManagerProps {
  inventory: InventoryItem[];
  usedParts: UsedPart[];
  onUpdateParts: (parts: UsedPart[]) => void;
}

export function PartsManager({ inventory, usedParts, onUpdateParts }: PartsManagerProps) {
  const [selectedPart, setSelectedPart] = useState("");
  const [partQuantity, setPartQuantity] = useState(1);
  const [otherPartName, setOtherPartName] = useState("");
  const [manufacturer, setManufacturer] = useState("");
  const [model, setModel] = useState("");
  const [serialNo, setSerialNo] = useState("");

  const addPartToInvoice = () => {
    if (selectedPart === "others" && otherPartName.trim()) {
      // Add custom part
      const newPart = { 
        itemId: "custom-" + Date.now(), 
        quantity: partQuantity, 
        name: otherPartName.trim(),
        manufacturer: manufacturer.trim() || undefined,
        model: model.trim() || undefined,
        serialNo: serialNo.trim() || undefined
      };
      onUpdateParts([...usedParts, newPart]);
      setOtherPartName("");
      setManufacturer("");
      setModel("");
      setSerialNo("");
      setSelectedPart("");
      setPartQuantity(1);
    } else if (selectedPart && selectedPart !== "others" && partQuantity > 0) {
      const part = inventory.find(item => item.id === selectedPart);
      if (part && part.currentStock >= partQuantity) {
        const newPart = { itemId: selectedPart, quantity: partQuantity, name: part.name };
        onUpdateParts([...usedParts, newPart]);
        setSelectedPart("");
        setPartQuantity(1);
      }
    }
  };

  const removePartFromInvoice = (index: number) => {
    onUpdateParts(usedParts.filter((_, i) => i !== index));
  };

  return (
    <PartsSection
      inventory={inventory}
      usedParts={usedParts}
      selectedPart={selectedPart}
      setSelectedPart={setSelectedPart}
      partQuantity={partQuantity}
      setPartQuantity={setPartQuantity}
      onAddPart={addPartToInvoice}
      onRemovePart={removePartFromInvoice}
    />
  );
}
