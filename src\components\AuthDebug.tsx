import { useSupabaseAuth } from "@/hooks/useSupabaseAuth";

export function AuthDebug() {
  const { user, session, loading } = useSupabaseAuth();

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 p-4 rounded shadow-lg text-sm z-50">
      <h3 className="font-bold mb-2">Auth Debug</h3>
      <div>Loading: {loading ? 'true' : 'false'}</div>
      <div>User: {user ? user.email : 'null'}</div>
      <div>Session: {session ? 'exists' : 'null'}</div>
      <div>User ID: {user?.id || 'null'}</div>
    </div>
  );
}
