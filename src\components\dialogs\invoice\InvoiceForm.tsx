import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import type { Invoice } from "../../../types";

interface InvoiceFormProps {
  formData: Invoice;
  setFormData: (data: Invoice) => void;
}

const stateRecommendations = [
  "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", 
  "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", 
  "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab", 
  "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", 
  "Uttarakhand", "West Bengal", "Delhi", "Jammu and Kashmir", "Ladakh"
];

const deviceTypes = ["Laptop", "Mobile/Tablet", "Notebook", "Others"];

const calculateExpectedDelivery = (billDate: string): string => {
  if (!billDate) return "";
  const date = new Date(billDate);
  date.setDate(date.getDate() + 3);
  return date.toISOString().split('T')[0];
};

export function InvoiceForm({ formData, setFormData }: InvoiceFormProps) {
  const [showCustomDevice, setShowCustomDevice] = useState(formData.deviceType === "Others");

  const handleDateChange = (date: string) => {
    const expectedDelivery = calculateExpectedDelivery(date);
    setFormData({
      ...formData, 
      date, 
      expectedDelivery
    });
  };

  const handleRemarksToggle = (checked: boolean) => {
    setFormData({
      ...formData,
      showRemarks: checked,
      remarks: checked ? formData.remarks : ""
    });
  };

  const handleDeviceTypeChange = (deviceType: string) => {
    setShowCustomDevice(deviceType === "Others");
    setFormData({
      ...formData,
      deviceType,
      customDeviceName: deviceType === "Others" ? formData.customDeviceName : "",
      device: deviceType === "Others" ? formData.customDeviceName || "" : formData.device
    });
  };

  const handleCustomDeviceNameChange = (customDeviceName: string) => {
    setFormData({
      ...formData,
      customDeviceName,
      device: customDeviceName
    });
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="customer">Customer Name</Label>
          <Input
            id="customer"
            value={formData.customer}
            onChange={(e) => setFormData({...formData, customer: e.target.value})}
            required
          />
        </div>
        <div>
          <Label htmlFor="date">Date</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleDateChange(e.target.value)}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="gst">GST (Optional)</Label>
          <Input
            id="gst"
            value={formData.gst || ""}
            onChange={(e) => setFormData({...formData, gst: e.target.value})}
            placeholder="GST Number"
          />
        </div>
        <div>
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={formData.phone || ""}
            onChange={(e) => setFormData({...formData, phone: e.target.value})}
            placeholder="+91 98765 43210"
          />
        </div>
        <div>
          <Label htmlFor="alternatePhone">Alternate Phone</Label>
          <Input
            id="alternatePhone"
            value={formData.alternatePhone || ""}
            onChange={(e) => setFormData({...formData, alternatePhone: e.target.value})}
            placeholder="+91 98765 43210"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="address">Address</Label>
        <Textarea
          id="address"
          value={formData.address || ""}
          onChange={(e) => setFormData({...formData, address: e.target.value})}
          placeholder="Complete address"
          rows={2}
        />
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="state">State</Label>
          <Select 
            value={formData.state || ""} 
            onValueChange={(value) => setFormData({...formData, state: value})}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select state" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50 max-h-48 overflow-y-auto">
              {stateRecommendations.map(state => (
                <SelectItem key={state} value={state}>{state}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="city">City</Label>
          <Input
            id="city"
            value={formData.city || ""}
            onChange={(e) => setFormData({...formData, city: e.target.value})}
            placeholder="City"
          />
        </div>
        <div>
          <Label htmlFor="pincode">Pincode</Label>
          <Input
            id="pincode"
            value={formData.pincode || ""}
            onChange={(e) => setFormData({...formData, pincode: e.target.value})}
            placeholder="123456"
            maxLength={6}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="deviceType">Device Type</Label>
          <Select 
            value={formData.deviceType || ""} 
            onValueChange={handleDeviceTypeChange}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select device type" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              {deviceTypes.map(type => (
                <SelectItem key={type} value={type}>{type}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="device">Device {showCustomDevice ? "Name" : ""}</Label>
          {showCustomDevice ? (
            <Input
              id="device"
              value={formData.customDeviceName || ""}
              onChange={(e) => handleCustomDeviceNameChange(e.target.value)}
              placeholder="Enter device name"
            />
          ) : (
            <Input
              id="device"
              value={formData.device || ""}
              onChange={(e) => setFormData({...formData, device: e.target.value})}
              placeholder="e.g., MacBook Pro"
            />
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="issue">Issue Description</Label>
        <Textarea
          id="issue"
          value={formData.issue || ""}
          onChange={(e) => setFormData({...formData, issue: e.target.value})}
          placeholder="Describe the problem"
          rows={2}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="estimatedAmount">Estimated Amount</Label>
          <Input
            id="estimatedAmount"
            placeholder="₹0"
            value={formData.estimatedAmount || ""}
            onChange={(e) => setFormData({...formData, estimatedAmount: e.target.value})}
          />
        </div>
        <div>
          <Label htmlFor="amount">Final Amount</Label>
          <Input
            id="amount"
            placeholder="₹0"
            value={formData.amount}
            onChange={(e) => setFormData({...formData, amount: e.target.value})}
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div>
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Paid">Paid</SelectItem>
              <SelectItem value="Overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="billableWarranty">Billable/Warranty</Label>
          <Select 
            value={formData.billableWarranty || ""} 
            onValueChange={(value) => setFormData({...formData, billableWarranty: value})}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              <SelectItem value="Billable">Billable</SelectItem>
              <SelectItem value="Warranty">Warranty</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div>
          <Label htmlFor="inspectionFee">Inspection Fee</Label>
          <Input
            id="inspectionFee"
            value={formData.inspectionFee || "500"}
            onChange={(e) => setFormData({...formData, inspectionFee: e.target.value})}
            placeholder="₹500"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="expectedDelivery">Expected Delivery Date</Label>
        <Input
          id="expectedDelivery"
          type="date"
          value={formData.expectedDelivery || ""}
          onChange={(e) => setFormData({...formData, expectedDelivery: e.target.value})}
          readOnly
          className="bg-gray-50"
        />
        <p className="text-xs text-gray-500 mt-1">Automatically calculated as 3 days after bill date</p>
      </div>

      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Switch
            id="remarks-toggle"
            checked={formData.showRemarks || false}
            onCheckedChange={handleRemarksToggle}
          />
          <Label htmlFor="remarks-toggle">Add Remarks</Label>
        </div>
        {formData.showRemarks && (
          <div>
            <Label htmlFor="remarks">Remarks</Label>
            <Textarea
              id="remarks"
              value={formData.remarks || ""}
              onChange={(e) => setFormData({...formData, remarks: e.target.value})}
              placeholder="Additional remarks or notes"
              rows={3}
            />
          </div>
        )}
      </div>
    </div>
  );
}
