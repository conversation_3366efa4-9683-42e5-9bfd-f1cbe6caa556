export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      comments: {
        Row: {
          id: string
          invoice_id: string | null
          service_id: string | null
          text: string
          author: string
          type: string
          created_at: string
        }
        Insert: {
          id?: string
          invoice_id?: string | null
          service_id?: string | null
          text: string
          author: string
          type: string
          created_at?: string
        }
        Update: {
          id?: string
          invoice_id?: string | null
          service_id?: string | null
          text?: string
          author?: string
          type?: string
          created_at?: string
        }
      }
      expenses: {
        Row: {
          id: string
          date: string
          description: string
          category: string
          amount: number
          vendor: string
          receipt: string | null
          invoice_id: string | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          date: string
          description: string
          category: string
          amount: number
          vendor: string
          receipt?: string | null
          invoice_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          date?: string
          description?: string
          category?: string
          amount?: number
          vendor?: string
          receipt?: string | null
          invoice_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
      }
      inventory_items: {
        Row: {
          id: string
          name: string
          description: string | null
          category: string | null
          price: number
          cost: number
          quantity: number
          reorder_level: number
          supplier: string | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          category?: string | null
          price: number
          cost: number
          quantity: number
          reorder_level: number
          supplier?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          category?: string | null
          price?: number
          cost?: number
          quantity?: number
          reorder_level?: number
          supplier?: string | null
          created_at?: string
          updated_at?: string | null
        }
      }
      invoices: {
        Row: {
          id: string
          customer_name: string
          date: string
          amount: number
          status: string
          device: string | null
          device_type: string | null
          custom_device_name: string | null
          issue: string | null
          gst: number | null
          phone: string | null
          alternate_phone: string | null
          address: string | null
          state: string | null
          city: string | null
          pincode: string | null
          estimated_amount: number | null
          billable_warranty: string | null
          show_remarks: boolean | null
          remarks: string | null
          expected_delivery: string | null
          inspection_fee: number | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          customer_name: string
          date: string
          amount: number
          status: string
          device?: string | null
          device_type?: string | null
          custom_device_name?: string | null
          issue?: string | null
          gst?: number | null
          phone?: string | null
          alternate_phone?: string | null
          address?: string | null
          state?: string | null
          city?: string | null
          pincode?: string | null
          estimated_amount?: number | null
          billable_warranty?: string | null
          show_remarks?: boolean | null
          remarks?: string | null
          expected_delivery?: string | null
          inspection_fee?: number | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          customer_name?: string
          date?: string
          amount?: number
          status?: string
          device?: string | null
          device_type?: string | null
          custom_device_name?: string | null
          issue?: string | null
          gst?: number | null
          phone?: string | null
          alternate_phone?: string | null
          address?: string | null
          state?: string | null
          city?: string | null
          pincode?: string | null
          estimated_amount?: number | null
          billable_warranty?: string | null
          show_remarks?: boolean | null
          remarks?: string | null
          expected_delivery?: string | null
          inspection_fee?: number | null
          created_at?: string
          updated_at?: string | null
        }
      }
      services: {
        Row: {
          id: string
          customer_name: string
          phone: string | null
          device: string | null
          issue: string | null
          status: string
          technician: string | null
          cost: number | null
          invoice_id: string | null
          created_at: string
          updated_at: string | null
        }
        Insert: {
          id?: string
          customer_name: string
          phone?: string | null
          device?: string | null
          issue?: string | null
          status: string
          technician?: string | null
          cost?: number | null
          invoice_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
        Update: {
          id?: string
          customer_name?: string
          phone?: string | null
          device?: string | null
          issue?: string | null
          status?: string
          technician?: string | null
          cost?: number | null
          invoice_id?: string | null
          created_at?: string
          updated_at?: string | null
        }
      }
      used_parts: {
        Row: {
          id: string
          invoice_id: string
          item_id: string | null
          name: string
          quantity: number
          price: number
          manufacturer: string | null
          model: string | null
          serial_no: string | null
          created_at: string
        }
        Insert: {
          id?: string
          invoice_id: string
          item_id?: string | null
          name: string
          quantity: number
          price: number
          manufacturer?: string | null
          model?: string | null
          serial_no?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          invoice_id?: string
          item_id?: string | null
          name?: string
          quantity?: number
          price?: number
          manufacturer?: string | null
          model?: string | null
          serial_no?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      comment_type: ["internal" | "customer" | "edit"]
      invoice_status: ["Pending" | "Paid" | "Overdue" | "Cancelled"]
      service_status: ["Pending" | "In Progress" | "Completed" | "Cancelled"]
    }
  }
}

export const Constants = {
  public: {
    Enums: {
      comment_type: ["internal", "customer", "edit"] as const,
      invoice_status: ["Pending", "Paid", "Overdue", "Cancelled"] as const,
      service_status: ["Pending", "In Progress", "Completed", "Cancelled"] as const
    }
  }
} as const
