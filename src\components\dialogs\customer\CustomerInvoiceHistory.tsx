
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Eye } from "lucide-react";

interface CustomerInvoiceHistoryProps {
  customerInvoices: any[];
  onViewInvoice: (invoiceId: string) => void;
}

export function CustomerInvoiceHistory({ customerInvoices, onViewInvoice }: CustomerInvoiceHistoryProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center space-x-2">
          <FileText className="w-5 h-5" />
          <span>Customer Invoice History</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 max-h-[400px] overflow-y-auto">
          {customerInvoices.length > 0 ? (
            customerInvoices.map((invoice) => (
              <div key={invoice.id} className="border-l-4 border-orange-200 pl-4 py-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">{invoice.id}</span>
                    <Badge className={
                      invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                      invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }>
                      {invoice.status}
                    </Badge>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewInvoice(invoice.id)}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
                <div className="text-sm text-gray-600">
                  <div>{invoice.date}</div>
                  {invoice.device && <div className="text-xs text-gray-500 mt-1">{invoice.device}</div>}
                  <div className="font-medium text-gray-900 mt-1">{invoice.amount}</div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500 text-center py-4">No invoices found for this customer</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
