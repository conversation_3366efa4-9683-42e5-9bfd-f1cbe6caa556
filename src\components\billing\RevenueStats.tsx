
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface RevenueStatsProps {
  totalRevenue: number;
  pendingRevenue: number;
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoicesCount: number;
}

export function RevenueStats({
  totalRevenue,
  pendingRevenue,
  totalInvoices,
  paidInvoices,
  pendingInvoicesCount
}: RevenueStatsProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium text-gray-600">Total Revenue (Paid)</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            ₹{totalRevenue.toLocaleString()}
          </div>
          <p className="text-sm text-gray-600 mt-1">From {paidInvoices} paid invoices</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium text-gray-600">Pending Revenue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">
            ₹{pendingRevenue.toLocaleString()}
          </div>
          <p className="text-sm text-gray-600 mt-1">From {pendingInvoicesCount} pending invoices</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium text-gray-600">Total Invoices</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-gray-900">{totalInvoices}</div>
          <p className="text-sm text-gray-600 mt-1">
            {paidInvoices} paid, {pendingInvoicesCount} pending
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
