
import { useState } from 'react';

interface Service {
  id: string;
  customer: string;
  phone: string;
  device: string;
  issue: string;
  status: string;
  technician: string;
  cost: string;
  invoiceId?: string;
  comments?: { text: string; author: string; timestamp: string; type: 'edit' | 'customer' | 'internal' | 'status_change' }[];
}

export function useServices() {
  const [services, setServices] = useState<Service[]>([]);

  const addService = (serviceData: Omit<Service, 'id'>): Service => {
    console.log('Adding service:', serviceData);
    const newService: Service = {
      ...serviceData,
      id: `SRV-${String(services.length + 1).padStart(3, '0')}`,
      status: serviceData.status || 'Pending',
      comments: serviceData.comments || []
    };
    setServices(prev => {
      const updated = [...prev, newService];
      console.log('Services updated:', updated);
      return updated;
    });
    return newService;
  };

  const updateService = (id: string, serviceData: Partial<Service>) => {
    console.log('Updating service:', id, serviceData);
    setServices(prev => {
      const existingService = prev.find(s => s.id === id);
      const updatedComments = [...(existingService?.comments || [])];
      
      if (existingService && serviceData.status && existingService.status !== serviceData.status) {
        updatedComments.push({
          text: `Service status changed from ${existingService.status} to ${serviceData.status}`,
          author: 'System',
          timestamp: new Date().toISOString(),
          type: 'status_change'
        });
      }
      
      const updated = prev.map(service =>
        service.id === id ? { ...service, ...serviceData, comments: updatedComments } : service
      );
      console.log('Services updated after edit:', updated);
      return updated;
    });
  };

  const deleteService = (id: string) => {
    setServices(prev => prev.filter(service => service.id !== id));
  };

  return {
    services,
    addService,
    updateService,
    deleteService,
    setServices
  };
}
