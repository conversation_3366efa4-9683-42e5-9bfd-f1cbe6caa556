import React, { createContext, useContext, useState } from 'react';
import { useCustomers } from '@/hooks/useCustomers';
import { useServices } from '@/hooks/useServices';
import { useInvoices } from '@/hooks/useInvoices';
import type { Customer, Service, Invoice, Expense, InventoryItem, UsedPart, Comment } from '../types';

interface AppDataContextType {
  customers: Customer[];
  services: Service[];
  invoices: Invoice[];
  expenses: Expense[];
  inventory: InventoryItem[];
  
  addCustomer: (customer: Omit<Customer, "id">) => Customer;
  addService: (service: Omit<Service, "id">) => Service;
  addInvoice: (invoice: Omit<Invoice, "id">) => Invoice;
  addExpense: (expense: Omit<Expense, 'id'>) => Expense;
  addInventory: (item: Omit<InventoryItem, 'id'>) => InventoryItem;
  updateInventory: (itemId: string, quantityUsed: number) => void;
  updateInventorySales: (itemId: string, quantity: number, unitPrice: number) => void;
  
  updateCustomer: (id: string, customer: Partial<Customer>) => void;
  updateService: (id: string, service: Partial<Service>) => void;
  updateInvoice: (id: string, invoice: Partial<Invoice>) => void;
  updateExpense: (id: string, expense: Partial<Expense>) => void;
  updateInventoryItem: (id: string, item: Partial<InventoryItem>) => void;
  
  deleteCustomer: (id: string) => void;
  deleteService: (id: string) => void;
  deleteInvoice: (id: string) => void;
  deleteExpense: (id: string) => void;
  deleteInventory: (id: string) => void;
  
  getLowStockItems: () => InventoryItem[];
  getPartsUsedToday: () => { name: string; quantity: number }[];
  getTodaysExpenses: () => number;
  getTodaySales: () => number;
}

const AppDataContext = createContext<AppDataContextType | undefined>(undefined);

export function AppDataProvider({ children }: { children: React.ReactNode }) {
  // Destructure from hooks. Aliased updateService and updateInvoice from hooks to avoid naming conflicts.
  const { customers, addCustomer, updateCustomer, deleteCustomer, setCustomers } = useCustomers();
  const { services, addService, updateService: hookUpdateService, deleteService, setServices } = useServices();
  const { invoices, addInvoice: addInvoiceHook, updateInvoice: hookUpdateInvoice, deleteInvoice: deleteInvoiceHook, setInvoices } = useInvoices();
  
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [inventory, setInventory] = useState<InventoryItem[]>([
    {
      id: 'INV-001',
      name: 'MacBook Pro Battery',
      category: 'Battery',
      currentStock: 15,
      minimumStock: 5,
      unitPrice: '₹7,500',
      supplier: 'Apple Inc.',
      location: 'Shelf A1',
      totalSales: 0,
      soldToday: 0
    },
    {
      id: 'INV-002',
      name: 'Laptop Screen 15.6"',
      category: 'Display',
      currentStock: 8,
      minimumStock: 3,
      unitPrice: '₹12,500',
      supplier: 'Tech Parts Co.',
      location: 'Shelf B2',
      totalSales: 0,
      soldToday: 0
    },
    {
      id: 'INV-003',
      name: 'Keyboard - Dell',
      category: 'Input Device',
      currentStock: 2,
      minimumStock: 5,
      unitPrice: '₹3,800',
      supplier: 'Dell Inc.',
      location: 'Shelf C1',
      totalSales: 0,
      soldToday: 0
    }
  ]);

  const addInvoiceWithLogic = (invoiceData: any) => {
    const newInvoice = addInvoiceHook(invoiceData);
    
    const existingCustomer = customers.find(c => c.name === invoiceData.customer);
    if (!existingCustomer && invoiceData.phone) {
      addCustomer({
        name: invoiceData.customer,
        email: `${invoiceData.customer.toLowerCase().replace(' ', '.')}@example.com`,
        phone: invoiceData.phone,
        alternatePhone: invoiceData.alternatePhone,
        address: invoiceData.address || 'Address to be updated',
        state: invoiceData.state,
        city: invoiceData.city,
        pincode: invoiceData.pincode
      });
    } else if (existingCustomer) {
      updateCustomer(existingCustomer.id, {
        phone: invoiceData.phone || existingCustomer.phone,
        alternatePhone: invoiceData.alternatePhone,
        address: invoiceData.address || existingCustomer.address,
        state: invoiceData.state || existingCustomer.state,
        city: invoiceData.city || existingCustomer.city,
        pincode: invoiceData.pincode || existingCustomer.pincode
      });
    }
    
    if (invoiceData.device && invoiceData.issue) {
      const serviceWithSameId = {
        id: newInvoice.id,
        customer: invoiceData.customer,
        phone: invoiceData.phone || '+91 98765 43210',
        device: invoiceData.device,
        issue: invoiceData.issue,
        status: 'Pending',
        technician: 'Not assigned',
        cost: invoiceData.amount,
        invoiceId: newInvoice.id,
        comments: []
      };
      setServices(prev => [...prev, serviceWithSameId]);
    }
    
    if (invoiceData.expenses && invoiceData.expenses.length > 0) {
      invoiceData.expenses.forEach((expense: any) => {
        const newExpense: Expense = {
          id: `EXP-${String(expenses.length + Date.now()).padStart(6, '0')}`,
          date: invoiceData.date,
          description: expense.description,
          category: expense.category,
          amount: expense.amount,
          vendor: "Internal",
          receipt: "Available",
          invoiceId: newInvoice.id
        };
        setExpenses(prev => [...prev, newExpense]);
      });
    }
    
    if (invoiceData.usedParts) {
      invoiceData.usedParts.forEach((part: any) => {
        if (!part.itemId.startsWith('custom-')) {
          updateInventory(part.itemId, part.quantity);
        }
      });
    }
    
    return newInvoice;
  };

  const deleteInvoiceWithLogic = (id: string) => {
    deleteInvoiceHook(id); // Use hook's delete function
    setExpenses(prev => prev.filter(expense => expense.invoiceId !== id));
  };

  const addExpense = (expenseData: Omit<Expense, 'id'>): Expense => {
    console.log('Adding manual expense:', expenseData);
    const newExpense: Expense = {
      ...expenseData,
      id: `EXP-${String(expenses.length + 1).padStart(3, '0')}`
    };
    setExpenses(prev => {
      const updated = [...prev, newExpense];
      console.log('Manual expenses updated:', updated);
      return updated;
    });
    return newExpense;
  };

  const addInventory = (itemData: Omit<InventoryItem, 'id'>): InventoryItem => {
    const newItem: InventoryItem = {
      ...itemData,
      id: `INV-${String(inventory.length + 1).padStart(3, '0')}`,
      totalSales: 0,
      soldToday: 0
    };
    setInventory(prev => [...prev, newItem]);
    return newItem;
  };

  const updateInventory = (itemId: string, quantityUsed: number) => {
    setInventory(prev => prev.map(item =>
      item.id === itemId
        ? { ...item, currentStock: Math.max(0, item.currentStock - quantityUsed) }
        : item
    ));
  };

  const updateInventorySales = (itemId: string, quantity: number, unitPrice: number) => {
    setInventory(prev => prev.map(item =>
      item.id === itemId
        ? { 
            ...item, 
            totalSales: (item.totalSales || 0) + (quantity * unitPrice),
            soldToday: (item.soldToday || 0) + quantity
          }
        : item
    ));
  };

  // updateCustomer is now directly from useCustomers hook, no local definition needed.
  // deleteCustomer is now directly from useCustomers hook, no local definition needed.
  // deleteService is now directly from useServices hook, no local definition needed.

  // This is the context's own updateService with additional logic
  const updateService = (id: string, serviceData: any) => {
    console.log('Updating service:', id, serviceData);
    const existingService = services.find(s => s.id === id);
    const updatedComments = [...(existingService?.comments || [])];
    
    if (existingService && serviceData.status && existingService.status !== serviceData.status) {
      updatedComments.push({
        text: `Service status changed from ${existingService.status} to ${serviceData.status}`,
        author: 'System',
        timestamp: new Date().toISOString(),
        type: 'status_change'
      });
      
      // Check if both invoice is paid and service is completed
      const associatedInvoice = invoices.find(inv => inv.id === id);
      if (associatedInvoice && 
          associatedInvoice.status === 'Paid' && 
          serviceData.status === 'Completed') {
        
        // Update inventory sales for used parts
        associatedInvoice.usedParts?.forEach(part => {
          if (!part.itemId.startsWith('custom-')) {
            const inventoryItem = inventory.find(item => item.id === part.itemId);
            if (inventoryItem) {
              const unitPrice = parseFloat(inventoryItem.unitPrice.replace(/[₹,]/g, ''));
              updateInventorySales(part.itemId, part.quantity, unitPrice);
            }
          }
        });
      }
    }
    
    // Use setServices from useServices hook for state update
    setServices(prev => {
      const updated = prev.map(service =>
        service.id === id ? { ...service, ...serviceData, comments: updatedComments } : service
      );
      console.log('Services updated after edit:', updated);
      return updated;
    });
    
    const associatedInvoice = invoices.find(inv => inv.id === id);
    if (associatedInvoice && serviceData.status && existingService?.status !== serviceData.status) {
      // Call the context's updateInvoice function
      updateInvoice(id, { // This will call the local updateInvoice defined below
        comments: [
          ...(associatedInvoice.comments || []),
          {
            text: `Associated service status changed from ${existingService?.status} to ${serviceData.status}`,
            author: 'System',
            timestamp: new Date().toISOString(),
            type: 'status_change'
          }
        ]
      });
    }
  };

  // This is the context's own updateInvoice with additional logic
  const updateInvoice = (id: string, invoiceData: any) => {
    console.log('Updating invoice:', id, invoiceData);
    // Use setInvoices from useInvoices hook for state update
    setInvoices(prev => {
      const updated = prev.map(invoice =>
        invoice.id === id ? { ...invoice, ...invoiceData } : invoice
      );
      console.log('Invoices updated after edit:', updated);
      return updated;
    });
    
    if (invoiceData.expenses) {
      setExpenses(prev => prev.filter(expense => expense.invoiceId !== id));
      const invoice = invoices.find(inv => inv.id === id) || // find current invoice or use the one from prev state before update
                      (invoices.find(inv => inv.id === id) ? { ...invoices.find(inv => inv.id === id), ...invoiceData } : null) ; // If not found, it implies new one or an issue. For safety, check.
      if (invoice) {
        invoiceData.expenses.forEach((expense: any) => {
          const newExpense: Expense = {
            id: `EXP-${String(expenses.length + Date.now()).padStart(6, '0')}`,
            date: invoice.date,
            description: expense.description,
            category: expense.category,
            amount: expense.amount,
            vendor: "Internal",
            receipt: "Available",
            invoiceId: id
          };
          setExpenses(prevExp => [...prevExp, newExpense]);
        });
      }
    }
  };

  const updateExpense = (id: string, expenseData: Partial<Expense>) => {
    setExpenses(prev => prev.map(expense =>
      expense.id === id ? { ...expense, ...expenseData } : expense
    ));
  };

  const updateInventoryItem = (id: string, itemData: Partial<InventoryItem>) => {
    setInventory(prev => prev.map(item =>
      item.id === id ? { ...item, ...itemData } : item
    ));
  };
  
  // deleteInvoice is aliased to deleteInvoiceWithLogic in the provider value below.

  const deleteExpense = (id: string) => {
    setExpenses(prev => prev.filter(expense => expense.id !== id));
  };

  const deleteInventory = (id: string) => {
    setInventory(prev => prev.filter(item => item.id !== id));
  };

  const getLowStockItems = (): InventoryItem[] => {
    return inventory.filter(item => item.currentStock <= item.minimumStock);
  };

  const getPartsUsedToday = (): { name: string; quantity: number }[] => {
    const today = new Date().toISOString().split('T')[0];
    const todaysInvoices = invoices.filter(inv => inv.date === today);
    
    const partsUsed: { [key: string]: number } = {};
    
    todaysInvoices.forEach(invoice => {
      invoice.usedParts?.forEach(part => {
        if (partsUsed[part.name]) {
          partsUsed[part.name] += part.quantity;
        } else {
          partsUsed[part.name] = part.quantity;
        }
      });
    });
    
    return Object.entries(partsUsed).map(([name, quantity]) => ({ name, quantity }));
  };

  const getTodaysExpenses = (): number => {
    const today = new Date().toISOString().split('T')[0];
    return expenses
      .filter(expense => expense.date === today)
      .reduce((sum, expense) => sum + parseFloat(expense.amount.replace(/[₹,]/g, '')), 0);
  };

  const getTodaySales = (): number => {
    return inventory.reduce((sum, item) => sum + (item.totalSales || 0), 0);
  };

  return (
    <AppDataContext.Provider value={{
      customers,
      services,
      invoices,
      expenses,
      inventory,
      addCustomer, // from useCustomers
      addService,  // from useServices
      addInvoice: addInvoiceWithLogic, // local enhanced version
      addExpense,
      addInventory,
      updateInventory,
      updateInventorySales,
      updateCustomer, // from useCustomers
      updateService,  // local enhanced version
      updateInvoice,  // local enhanced version
      updateExpense,
      updateInventoryItem,
      deleteCustomer, // from useCustomers
      deleteService,  // from useServices
      deleteInvoice: deleteInvoiceWithLogic, // local enhanced version
      deleteExpense,
      deleteInventory,
      getLowStockItems,
      getPartsUsedToday,
      getTodaysExpenses,
      getTodaySales
    }}>
      {children}
    </AppDataContext.Provider>
  );
}

export function useAppData() {
  const context = useContext(AppDataContext);
  if (context === undefined) {
    throw new Error('useAppData must be used within an AppDataProvider');
  }
  return context;
}
