
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Layout } from "./components/Layout";
import { SupabaseAppDataProvider } from "./contexts/SupabaseAppDataContext";
import { useSupabaseAuth } from "./hooks/useSupabaseAuth";
import { AuthDebug } from "./components/AuthDebug";
import Dashboard from "./pages/Dashboard";
import Billing from "./pages/Billing";
import Services from "./pages/Services";
import Customers from "./pages/Customers";
import Reports from "./pages/Reports";
import Expenses from "./pages/Expenses";
import Inventory from "./pages/Inventory";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
});

// Protected route component
function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useSupabaseAuth();

  console.log('ProtectedRoute - loading:', loading, 'user:', user?.email);

  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }

  if (!user) {
    console.log('No user found, redirecting to login');
    return <Navigate to="/login" replace />;
  }

  console.log('User authenticated, rendering protected content');
  return <>{children}</>;
}

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthDebug />
      <BrowserRouter>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
          
          <Route path="/" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Dashboard />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/billing" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Billing />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/services" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Services />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/customers" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Customers />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/inventory" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Inventory />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/reports" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Reports />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/expenses" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Expenses />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="/settings" element={
            <ProtectedRoute>
              <SupabaseAppDataProvider>
                <Layout>
                  <Settings />
                </Layout>
              </SupabaseAppDataProvider>
            </ProtectedRoute>
          } />
          
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
