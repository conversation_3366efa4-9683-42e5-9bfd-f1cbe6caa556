
import { useState } from 'react';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  alternatePhone?: string;
  address: string;
  state?: string;
  city?: string;
  pincode?: string;
  purchases?: number;
  services?: number;
  totalSpent?: string;
  warranty?: string;
}

export function useCustomers() {
  const [customers, setCustomers] = useState<Customer[]>([]);

  const addCustomer = (customerData: Omit<Customer, 'id'>): Customer => {
    const newCustomer: Customer = {
      ...customerData,
      id: `CUST-${String(customers.length + 1).padStart(3, '0')}`,
      purchases: 0,
      services: 0,
      totalSpent: '₹0',
      warranty: 'N/A'
    };
    setCustomers(prev => [...prev, newCustomer]);
    return newCustomer;
  };

  const updateCustomer = (id: string, customerData: Partial<Customer>) => {
    setCustomers(prev => prev.map(customer =>
      customer.id === id ? { ...customer, ...customerData } : customer
    ));
  };

  const deleteCustomer = (id: string) => {
    setCustomers(prev => prev.filter(customer => customer.id !== id));
  };

  return {
    customers,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    setCustomers
  };
}
